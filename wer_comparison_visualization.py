import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Polygon

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置图表样式
plt.style.use('default')
plt.rcParams['figure.facecolor'] = 'white'
plt.rcParams['axes.facecolor'] = 'white'

def create_comparison_data():
    """创建比较数据"""
    
    # 旧结果数据 (new1_wer_results.txt)
    old_data = {
        '第1行(duihua1)': 38.91,
        '第2行(duihua2)': 29.41,
        '第3行(duihua3)': 65.07,
        '第5行(download)': 67.13,
        '第6行(beiwanglu1)': 33.55,
        '第7行(beiwanglu2)': 22.71,
        '第8行(miantan1)': 19.35,
        '第9行(miantan2)': 19.05,
        '第10行(miantan3)': 19.78
    }
    
    # 新结果数据 (transcripts_wer_summary.txt)
    new_data = {
        '第1行(duihua1)': 5.46,
        '第2行(duihua2)': 24.78,
        '第3行(duihua3)': 15.58,
        '第5行(download)': 19.38,
        '第6行(beiwanglu1)': 19.60,
        '第7行(beiwanglu2)': 3.39,
        '第8行(miantan1)': 3.98,
        '第9行(miantan2)': 3.88,
        '第10行(miantan3)': 4.99
    }
    
    # 计算改进幅度
    improvement = {}
    for key in old_data:
        improvement[key] = old_data[key] - new_data[key]
    
    return old_data, new_data, improvement

def plot_line_comparison():
    """绘制折线图比较"""
    old_data, new_data, improvement = create_comparison_data()
    
    labels = list(old_data.keys())
    old_values = list(old_data.values())
    new_values = list(new_data.values())
    
    plt.figure(figsize=(14, 8))
    
    x = np.arange(len(labels))
    
    plt.plot(x, old_values, 'o-', linewidth=3, markersize=8, label='优化前WER', color='#ff6b6b', alpha=0.8)
    plt.plot(x, new_values, 's-', linewidth=3, markersize=8, label='优化后WER', color='#4ecdc4', alpha=0.8)
    
    # 添加改进箭头
    for i, (old, new) in enumerate(zip(old_values, new_values)):
        if old > new:
            plt.annotate('', xy=(i, new), xytext=(i, old),
                        arrowprops=dict(arrowstyle='->', color='green', lw=2, alpha=0.7))
            plt.text(i, (old + new) / 2, f'-{old-new:.1f}%',
                    ha='center', va='center', fontsize=9,
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.7))
    
    plt.xlabel('文件类型', fontsize=12, fontweight='bold')
    plt.ylabel('WER (%)', fontsize=12, fontweight='bold')
    plt.title('ASR系统WER优化前后对比\n(数值越低越好)', fontsize=16, fontweight='bold', pad=20)
    
    plt.xticks(x, [label.split('(')[1].replace(')', '') for label in labels], rotation=45, ha='right')
    plt.legend(fontsize=12, loc='upper right')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    # 添加总体改进信息
    old_avg = np.mean(old_values)
    new_avg = np.mean(new_values)
    plt.text(0.02, 0.98, f'平均WER改进: {old_avg:.1f}% → {new_avg:.1f}% (改进{old_avg-new_avg:.1f}%)', 
             transform=plt.gca().transAxes, fontsize=11, fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.8),
             verticalalignment='top')
    
    plt.savefig('wer_line_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_radar_chart():
    """绘制雷达图比较"""
    old_data, new_data, improvement = create_comparison_data()
    
    # 准备数据
    categories = [label.split('(')[1].replace(')', '') for label in old_data.keys()]
    old_values = list(old_data.values())
    new_values = list(new_data.values())
    
    # 计算角度
    N = len(categories)
    angles = [n / float(N) * 2 * np.pi for n in range(N)]
    angles += angles[:1]  # 闭合图形
    
    # 闭合数据
    old_values += old_values[:1]
    new_values += new_values[:1]
    
    fig, ax = plt.subplots(figsize=(12, 12), subplot_kw=dict(projection='polar'))
    
    # 绘制雷达图
    ax.plot(angles, old_values, 'o-', linewidth=3, label='优化前WER', color='#ff6b6b', alpha=0.8)
    ax.fill(angles, old_values, alpha=0.25, color='#ff6b6b')
    
    ax.plot(angles, new_values, 's-', linewidth=3, label='优化后WER', color='#4ecdc4', alpha=0.8)
    ax.fill(angles, new_values, alpha=0.25, color='#4ecdc4')
    
    # 设置标签
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories, fontsize=11)
    
    # 设置y轴
    ax.set_ylim(0, max(max(old_values), max(new_values)) * 1.1)
    ax.set_ylabel('WER (%)', fontsize=12, fontweight='bold')
    ax.grid(True)
    
    # 添加标题和图例
    plt.title('ASR系统WER雷达图对比\n(越靠近中心越好)', fontsize=16, fontweight='bold', pad=30)
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=12)
    
    plt.tight_layout()
    plt.savefig('wer_radar_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_improvement_bar():
    """绘制改进幅度柱状图"""
    old_data, new_data, improvement = create_comparison_data()
    
    labels = [label.split('(')[1].replace(')', '') for label in improvement.keys()]
    improvements = list(improvement.values())
    
    # 按改进幅度排序
    sorted_data = sorted(zip(labels, improvements), key=lambda x: x[1], reverse=True)
    labels, improvements = zip(*sorted_data)
    
    plt.figure(figsize=(12, 8))
    
    # 设置颜色：改进大的用绿色，改进小的用橙色
    colors = ['#2ecc71' if imp > 10 else '#f39c12' if imp > 5 else '#e74c3c' for imp in improvements]
    
    bars = plt.bar(labels, improvements, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    
    # 添加数值标签
    for bar, imp in zip(bars, improvements):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{imp:.1f}%', ha='center', va='bottom', fontsize=11, fontweight='bold')
    
    plt.xlabel('文件类型', fontsize=12, fontweight='bold')
    plt.ylabel('WER改进幅度 (%)', fontsize=12, fontweight='bold')
    plt.title('各文件类型WER改进幅度对比\n(数值越高改进越大)', fontsize=16, fontweight='bold', pad=20)
    
    plt.xticks(rotation=45, ha='right')
    plt.grid(axis='y', alpha=0.3)
    
    # 添加改进等级说明
    plt.text(0.02, 0.98, '改进等级:\n🟢 显著改进 (>10%)\n🟠 明显改进 (5-10%)\n🔴 轻微改进 (<5%)', 
             transform=plt.gca().transAxes, fontsize=10,
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8),
             verticalalignment='top')
    
    plt.tight_layout()
    plt.savefig('wer_improvement_bar.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_category_performance():
    """绘制文件类型性能分析"""
    old_data, new_data, improvement = create_comparison_data()
    
    # 按文件类型分组
    categories = {
        '对话类': ['duihua1', 'duihua2', 'duihua3'],
        '面谈类': ['miantan1', 'miantan2', 'miantan3'],
        '备忘录类': ['beiwanglu1', 'beiwanglu2'],
        '其他': ['download']
    }
    
    category_old = {}
    category_new = {}
    
    for cat, files in categories.items():
        old_vals = []
        new_vals = []
        for file in files:
            for key in old_data:
                if file in key:
                    old_vals.append(old_data[key])
                    new_vals.append(new_data[key])
        category_old[cat] = np.mean(old_vals) if old_vals else 0
        category_new[cat] = np.mean(new_vals) if new_vals else 0
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # 左图：分类平均WER对比
    categories_list = list(category_old.keys())
    x = np.arange(len(categories_list))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, list(category_old.values()), width, 
                   label='优化前', color='#ff6b6b', alpha=0.8)
    bars2 = ax1.bar(x + width/2, list(category_new.values()), width,
                   label='优化后', color='#4ecdc4', alpha=0.8)
    
    ax1.set_xlabel('文件类型', fontweight='bold')
    ax1.set_ylabel('平均WER (%)', fontweight='bold')
    ax1.set_title('各类型文件平均WER对比', fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels(categories_list)
    ax1.legend()
    ax1.grid(axis='y', alpha=0.3)
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{height:.1f}%', ha='center', va='bottom', fontsize=10)
    
    # 右图：改进幅度
    improvements = [category_old[cat] - category_new[cat] for cat in categories_list]
    bars3 = ax2.bar(categories_list, improvements, 
                   color=['#2ecc71' if imp > 15 else '#f39c12' for imp in improvements], 
                   alpha=0.8)
    
    ax2.set_xlabel('文件类型', fontweight='bold')
    ax2.set_ylabel('WER改进幅度 (%)', fontweight='bold')
    ax2.set_title('各类型文件WER改进幅度', fontweight='bold')
    ax2.grid(axis='y', alpha=0.3)
    
    # 添加数值标签
    for bar, imp in zip(bars3, improvements):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{imp:.1f}%', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('wer_category_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_summary_report():
    """生成总结报告"""
    old_data, new_data, improvement = create_comparison_data()
    
    print("="*60)
    print("ASR系统WER优化效果总结报告")
    print("="*60)
    
    # 总体统计
    old_avg = np.mean(list(old_data.values()))
    new_avg = np.mean(list(new_data.values()))
    total_improvement = old_avg - new_avg
    
    print(f"\n📊 总体改进效果:")
    print(f"   优化前平均WER: {old_avg:.2f}%")
    print(f"   优化后平均WER: {new_avg:.2f}%")
    print(f"   总体改进幅度: {total_improvement:.2f}% (相对改进: {total_improvement/old_avg*100:.1f}%)")
    
    # 最佳和最差改进
    best_improvement = max(improvement.items(), key=lambda x: x[1])
    worst_improvement = min(improvement.items(), key=lambda x: x[1])
    
    print(f"\n🏆 最佳改进:")
    print(f"   {best_improvement[0]}: 改进了 {best_improvement[1]:.2f}%")
    print(f"   ({old_data[best_improvement[0]]:.2f}% → {new_data[best_improvement[0]]:.2f}%)")
    
    print(f"\n⚠️  需要关注:")
    print(f"   {worst_improvement[0]}: 改进了 {worst_improvement[1]:.2f}%")
    print(f"   ({old_data[worst_improvement[0]]:.2f}% → {new_data[worst_improvement[0]]:.2f}%)")
    
    # 改进等级分析
    excellent = sum(1 for imp in improvement.values() if imp > 20)
    good = sum(1 for imp in improvement.values() if 10 <= imp <= 20)
    moderate = sum(1 for imp in improvement.values() if 5 <= imp < 10)
    slight = sum(1 for imp in improvement.values() if imp < 5)
    
    print(f"\n📈 改进等级分布:")
    print(f"   🟢 显著改进 (>20%): {excellent} 个文件")
    print(f"   🟡 良好改进 (10-20%): {good} 个文件")
    print(f"   🟠 中等改进 (5-10%): {moderate} 个文件")
    print(f"   🔴 轻微改进 (<5%): {slight} 个文件")
    
    print("\n" + "="*60)

if __name__ == "__main__":
    print("正在生成WER对比可视化图表...")
    
    # 生成各种图表
    plot_line_comparison()
    plot_radar_chart()
    plot_improvement_bar()
    plot_category_performance()
    
    # 生成总结报告
    generate_summary_report()
    
    print("\n✅ 所有图表已生成完成！")
    print("📁 生成的文件:")
    print("   - wer_line_comparison.png (折线图对比)")
    print("   - wer_radar_comparison.png (雷达图对比)")
    print("   - wer_improvement_bar.png (改进幅度柱状图)")
    print("   - wer_category_analysis.png (分类性能分析)")
