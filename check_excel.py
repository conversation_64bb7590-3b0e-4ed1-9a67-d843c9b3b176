import pandas as pd

# 读取Excel文件
df = pd.read_excel('new1.xlsx')

print("Excel文件内容:")
print("=" * 80)
print(f"总行数: {len(df)}")
print(f"总列数: {len(df.columns)}")
print()

print("列名:")
for i, col in enumerate(df.columns):
    print(f"第{i+1}列: {col}")
print()

print("所有数据:")
print(df.to_string())
print()

print("查找包含'备忘录'的内容:")
for idx, row in df.iterrows():
    for col in df.columns:
        cell_value = str(row[col]) if pd.notna(row[col]) else ""
        if '备忘录' in cell_value:
            print(f"第{idx+1}行, 列'{col}': {cell_value}")

print("\n查找包含'beiwang'的内容:")
for idx, row in df.iterrows():
    for col in df.columns:
        cell_value = str(row[col]).lower() if pd.notna(row[col]) else ""
        if 'beiwang' in cell_value:
            print(f"第{idx+1}行, 列'{col}': {cell_value}")
