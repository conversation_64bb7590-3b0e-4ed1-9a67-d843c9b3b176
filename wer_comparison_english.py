import matplotlib.pyplot as plt
import numpy as np

# 设置图表样式
plt.style.use('default')
plt.rcParams['figure.facecolor'] = 'white'
plt.rcParams['axes.facecolor'] = 'white'
plt.rcParams['font.size'] = 10

def create_comparison_data():
    """创建比较数据"""
    
    # 旧结果数据 (new1_wer_results.txt)
    old_data = {
        'duihua1': 38.91,
        'duihua2': 29.41,
        'duihua3': 65.07,
        'download': 67.13,
        'beiwanglu1': 33.55,
        'beiwanglu2': 22.71,
        'miantan1': 19.35,
        'miantan2': 19.05,
        'miantan3': 19.78
    }
    
    # 新结果数据 (transcripts_wer_summary.txt)
    new_data = {
        'duihua1': 5.46,
        'duihua2': 24.78,
        'duihua3': 15.58,
        'download': 19.38,
        'beiwanglu1': 19.60,
        'beiwanglu2': 3.39,
        'miantan1': 3.98,
        'miantan2': 3.88,
        'miantan3': 4.99
    }
    
    # 计算改进幅度
    improvement = {}
    for key in old_data:
        improvement[key] = old_data[key] - new_data[key]
    
    return old_data, new_data, improvement

def plot_line_comparison():
    """绘制折线图比较"""
    old_data, new_data, improvement = create_comparison_data()
    
    labels = list(old_data.keys())
    old_values = list(old_data.values())
    new_values = list(new_data.values())
    
    plt.figure(figsize=(14, 8))
    
    x = np.arange(len(labels))
    
    plt.plot(x, old_values, 'o-', linewidth=3, markersize=8, label='Before Optimization', color='#ff6b6b', alpha=0.8)
    plt.plot(x, new_values, 's-', linewidth=3, markersize=8, label='After Optimization', color='#4ecdc4', alpha=0.8)
    
    # 添加改进箭头和标签
    for i, (old, new) in enumerate(zip(old_values, new_values)):
        if old > new:
            plt.annotate('', xy=(i, new), xytext=(i, old),
                        arrowprops=dict(arrowstyle='->', color='green', lw=2, alpha=0.7))
            plt.text(i, (old + new) / 2, f'-{old-new:.1f}%', 
                    ha='center', va='center', fontsize=9, fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.7))
    
    plt.xlabel('File Type', fontsize=12, fontweight='bold')
    plt.ylabel('WER (%)', fontsize=12, fontweight='bold')
    plt.title('ASR System WER Comparison: Before vs After Optimization\n(Lower is Better)', fontsize=16, fontweight='bold', pad=20)
    
    plt.xticks(x, labels, rotation=45, ha='right')
    plt.legend(fontsize=12, loc='upper right')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    # 添加总体改进信息
    old_avg = np.mean(old_values)
    new_avg = np.mean(new_values)
    plt.text(0.02, 0.98, f'Average WER: {old_avg:.1f}% → {new_avg:.1f}% (Improved {old_avg-new_avg:.1f}%)', 
             transform=plt.gca().transAxes, fontsize=11, fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.8),
             verticalalignment='top')
    
    plt.savefig('wer_line_comparison_en.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_radar_chart():
    """绘制雷达图比较"""
    old_data, new_data, improvement = create_comparison_data()
    
    # 准备数据
    categories = list(old_data.keys())
    old_values = list(old_data.values())
    new_values = list(new_data.values())
    
    # 计算角度
    N = len(categories)
    angles = [n / float(N) * 2 * np.pi for n in range(N)]
    angles += angles[:1]  # 闭合图形
    
    # 闭合数据
    old_values += old_values[:1]
    new_values += new_values[:1]
    
    fig, ax = plt.subplots(figsize=(12, 12), subplot_kw=dict(projection='polar'))
    
    # 绘制雷达图
    ax.plot(angles, old_values, 'o-', linewidth=3, label='Before Optimization', color='#ff6b6b', alpha=0.8)
    ax.fill(angles, old_values, alpha=0.25, color='#ff6b6b')
    
    ax.plot(angles, new_values, 's-', linewidth=3, label='After Optimization', color='#4ecdc4', alpha=0.8)
    ax.fill(angles, new_values, alpha=0.25, color='#4ecdc4')
    
    # 设置标签
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories, fontsize=11)
    
    # 设置y轴
    ax.set_ylim(0, max(max(old_values), max(new_values)) * 1.1)
    ax.set_ylabel('WER (%)', fontsize=12, fontweight='bold')
    ax.grid(True)
    
    # 添加标题和图例
    plt.title('ASR System WER Radar Chart Comparison\n(Closer to Center is Better)', fontsize=16, fontweight='bold', pad=30)
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=12)
    
    plt.tight_layout()
    plt.savefig('wer_radar_comparison_en.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_improvement_bar():
    """绘制改进幅度柱状图"""
    old_data, new_data, improvement = create_comparison_data()
    
    labels = list(improvement.keys())
    improvements = list(improvement.values())
    
    # 按改进幅度排序
    sorted_data = sorted(zip(labels, improvements), key=lambda x: x[1], reverse=True)
    labels, improvements = zip(*sorted_data)
    
    plt.figure(figsize=(12, 8))
    
    # 设置颜色：改进大的用绿色，改进小的用橙色
    colors = ['#2ecc71' if imp > 20 else '#f39c12' if imp > 10 else '#e74c3c' for imp in improvements]
    
    bars = plt.bar(labels, improvements, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    
    # 添加数值标签
    for bar, imp in zip(bars, improvements):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{imp:.1f}%', ha='center', va='bottom', fontsize=11, fontweight='bold')
    
    plt.xlabel('File Type', fontsize=12, fontweight='bold')
    plt.ylabel('WER Improvement (%)', fontsize=12, fontweight='bold')
    plt.title('WER Improvement by File Type\n(Higher Values = Better Improvement)', fontsize=16, fontweight='bold', pad=20)
    
    plt.xticks(rotation=45, ha='right')
    plt.grid(axis='y', alpha=0.3)
    
    # 添加改进等级说明
    plt.text(0.02, 0.98, 'Improvement Levels:\nGreen: Excellent (>20%)\nOrange: Good (10-20%)\nRed: Moderate (<10%)', 
             transform=plt.gca().transAxes, fontsize=10,
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8),
             verticalalignment='top')
    
    plt.tight_layout()
    plt.savefig('wer_improvement_bar_en.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_category_performance():
    """绘制文件类型性能分析"""
    old_data, new_data, improvement = create_comparison_data()
    
    # 按文件类型分组
    categories = {
        'Dialogue': ['duihua1', 'duihua2', 'duihua3'],
        'Interview': ['miantan1', 'miantan2', 'miantan3'],
        'Memo': ['beiwanglu1', 'beiwanglu2'],
        'Other': ['download']
    }
    
    category_old = {}
    category_new = {}
    
    for cat, files in categories.items():
        old_vals = []
        new_vals = []
        for file in files:
            if file in old_data:
                old_vals.append(old_data[file])
                new_vals.append(new_data[file])
        category_old[cat] = np.mean(old_vals) if old_vals else 0
        category_new[cat] = np.mean(new_vals) if new_vals else 0
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # 左图：分类平均WER对比
    categories_list = list(category_old.keys())
    x = np.arange(len(categories_list))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, list(category_old.values()), width, 
                   label='Before', color='#ff6b6b', alpha=0.8)
    bars2 = ax1.bar(x + width/2, list(category_new.values()), width,
                   label='After', color='#4ecdc4', alpha=0.8)
    
    ax1.set_xlabel('File Category', fontweight='bold')
    ax1.set_ylabel('Average WER (%)', fontweight='bold')
    ax1.set_title('Average WER by File Category', fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels(categories_list)
    ax1.legend()
    ax1.grid(axis='y', alpha=0.3)
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{height:.1f}%', ha='center', va='bottom', fontsize=10)
    
    # 右图：改进幅度
    improvements = [category_old[cat] - category_new[cat] for cat in categories_list]
    bars3 = ax2.bar(categories_list, improvements, 
                   color=['#2ecc71' if imp > 15 else '#f39c12' for imp in improvements], 
                   alpha=0.8)
    
    ax2.set_xlabel('File Category', fontweight='bold')
    ax2.set_ylabel('WER Improvement (%)', fontweight='bold')
    ax2.set_title('WER Improvement by Category', fontweight='bold')
    ax2.grid(axis='y', alpha=0.3)
    
    # 添加数值标签
    for bar, imp in zip(bars3, improvements):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{imp:.1f}%', ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('wer_category_analysis_en.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_summary_report():
    """生成总结报告"""
    old_data, new_data, improvement = create_comparison_data()
    
    print("="*60)
    print("ASR System WER Optimization Summary Report")
    print("="*60)
    
    # 总体统计
    old_avg = np.mean(list(old_data.values()))
    new_avg = np.mean(list(new_data.values()))
    total_improvement = old_avg - new_avg
    
    print(f"\n📊 Overall Improvement:")
    print(f"   Before Optimization: {old_avg:.2f}%")
    print(f"   After Optimization: {new_avg:.2f}%")
    print(f"   Total Improvement: {total_improvement:.2f}% (Relative: {total_improvement/old_avg*100:.1f}%)")
    
    # 最佳和最差改进
    best_improvement = max(improvement.items(), key=lambda x: x[1])
    worst_improvement = min(improvement.items(), key=lambda x: x[1])
    
    print(f"\n🏆 Best Improvement:")
    print(f"   {best_improvement[0]}: Improved by {best_improvement[1]:.2f}%")
    print(f"   ({old_data[best_improvement[0]]:.2f}% → {new_data[best_improvement[0]]:.2f}%)")
    
    print(f"\n⚠️  Needs Attention:")
    print(f"   {worst_improvement[0]}: Improved by {worst_improvement[1]:.2f}%")
    print(f"   ({old_data[worst_improvement[0]]:.2f}% → {new_data[worst_improvement[0]]:.2f}%)")
    
    # 改进等级分析
    excellent = sum(1 for imp in improvement.values() if imp > 20)
    good = sum(1 for imp in improvement.values() if 10 <= imp <= 20)
    moderate = sum(1 for imp in improvement.values() if 5 <= imp < 10)
    slight = sum(1 for imp in improvement.values() if imp < 5)
    
    print(f"\n📈 Improvement Level Distribution:")
    print(f"   Excellent (>20%): {excellent} files")
    print(f"   Good (10-20%): {good} files")
    print(f"   Moderate (5-10%): {moderate} files")
    print(f"   Slight (<5%): {slight} files")
    
    print("\n" + "="*60)

if __name__ == "__main__":
    print("Generating WER comparison visualizations...")
    
    # 生成各种图表
    plot_line_comparison()
    plot_radar_chart()
    plot_improvement_bar()
    plot_category_performance()
    
    # 生成总结报告
    generate_summary_report()
    
    print("\n✅ All charts generated successfully!")
    print("📁 Generated files:")
    print("   - wer_line_comparison_en.png (Line Chart)")
    print("   - wer_radar_comparison_en.png (Radar Chart)")
    print("   - wer_improvement_bar_en.png (Improvement Bar Chart)")
    print("   - wer_category_analysis_en.png (Category Analysis)")
