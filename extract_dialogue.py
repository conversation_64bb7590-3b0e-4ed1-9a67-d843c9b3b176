import pandas as pd
import re

def extract_dialogue_texts(excel_file):
    """
    从Excel文件第三列提取对话文本，分别输出客户和坐席的文本
    """
    # 读取Excel文件
    df = pd.read_excel(excel_file)
    
    # 获取第三列数据（索引为2）
    dialogue_column = df.iloc[:, 2]
    
    customer_texts = []
    agent_texts = []
    
    for idx, text in enumerate(dialogue_column):
        if pd.isna(text):  # 跳过空值
            continue
            
        print(f"处理第 {idx+1} 行对话...")
        
        # 分离客户和坐席的文本
        customer_parts = []
        agent_parts = []
        
        # 按行分割文本
        lines = str(text).split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 提取客户文本
            if line.startswith('客户：'):
                customer_text = line.replace('客户：', '').strip()
                # 去掉(空)标记
                customer_text = customer_text.replace('（空）', '').replace('(空)', '').strip()
                if customer_text:
                    customer_parts.append(customer_text)
            
            # 提取坐席文本
            elif line.startswith('坐席：'):
                agent_text = line.replace('坐席：', '').strip()
                # 去掉(空)标记
                agent_text = agent_text.replace('（空）', '').replace('(空)', '').strip()
                if agent_text:
                    agent_parts.append(agent_text)
        
        # 合并同一对话中的客户和坐席文本
        if customer_parts:
            customer_texts.append(' '.join(customer_parts))
        
        if agent_parts:
            agent_texts.append(' '.join(agent_parts))
    
    return customer_texts, agent_texts

def save_combined_texts_to_file(customer_texts, agent_texts, output_file):
    """
    将每个对话的客户和坐席文本拼接成完整对话保存到文件
    """
    with open(output_file, 'w', encoding='utf-8') as f:
        # 确保客户和坐席文本数量一致
        min_length = min(len(customer_texts), len(agent_texts))
        
        for i in range(min_length):
            # 将客户文本和坐席文本拼接成一个完整对话
            combined_text = customer_texts[i] + " " + agent_texts[i]
            f.write(f"对话{i+1}：{combined_text}\n\n")

if __name__ == "__main__":
    # 提取对话文本
    customer_texts, agent_texts = extract_dialogue_texts('asr.xlsx')
    
    print(f"提取到 {len(customer_texts)} 条客户文本")
    print(f"提取到 {len(agent_texts)} 条坐席文本")
    
    # 保存到文件
    save_combined_texts_to_file(customer_texts, agent_texts, 'dialogue_texts.txt')
    
    print("文本已保存到 dialogue_texts.txt")
    
    # 预览前几条
    print("\n客户文本预览:")
    for i, text in enumerate(customer_texts[:3], 1):
        print(f"客户{i}：{text[:100]}...")
    
    print("\n坐席文本预览:")
    for i, text in enumerate(agent_texts[:3], 1):
        print(f"坐席{i}：{text[:100]}...")