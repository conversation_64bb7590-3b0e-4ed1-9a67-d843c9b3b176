#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os
import subprocess
import sys
import re
from typing import Tuple, Optional

class SimpleChineseWERCalculator:
    """简单的中文WER计算器 - 专注于原始文本+字符级WER"""
    
    def __init__(self, output_dir: str = "./wer_results/"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def clean_text(self, text: str) -> str:
        """清理文本，移除标点符号和多余空格"""
        if pd.isna(text) or text is None:
            return ""
        
        text = str(text).strip()
        # 移除常见标点符号
        punctuation = ['。', '，', '！', '？', '；', '：', '、', '"', '"', ''', ''', 
                      '（', '）', '【', '】', '《', '》', '…', '—', '·']
        for p in punctuation:
            text = text.replace(p, '')
        
        # 移除多余空格
        text = re.sub(r'\s+', '', text)
        return text
    
    def create_wer_files(self, excel_file: str, ref_col: str = 'text', 
                        hyp_col: str = '复核后文本') -> Tuple[str, str]:
        """
        从Excel文件创建WER计算用的.ref和.hyp文件（原始文本格式）
        """
        print(f"正在读取Excel文件: {excel_file}")
        
        try:
            df = pd.read_excel(excel_file)
            print(f"成功读取 {len(df)} 行数据")
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return None, None
        
        # 检查列是否存在
        if ref_col not in df.columns:
            print(f"错误: 找不到参考文本列 '{ref_col}'")
            print(f"可用列: {df.columns.tolist()}")
            return None, None
        
        if hyp_col not in df.columns:
            print(f"错误: 找不到假设文本列 '{hyp_col}'")
            print(f"可用列: {df.columns.tolist()}")
            return None, None
        
        # 过滤空值
        original_len = len(df)
        df = df.dropna(subset=[ref_col, hyp_col])
        filtered_len = len(df)
        
        if filtered_len < original_len:
            print(f"过滤掉 {original_len - filtered_len} 行空值数据，剩余 {filtered_len} 行")
        
        if filtered_len == 0:
            print("错误: 没有有效的数据行")
            return None, None
        
        # 定义输出文件路径
        ref_file = os.path.join(self.output_dir, "test.ref")
        hyp_file = os.path.join(self.output_dir, "test.hyp")
        
        print(f"创建参考文件: {ref_file}")
        
        # 创建参考文件（原始文本，只清理标点）
        with open(ref_file, 'w', encoding='utf-8') as f:
            for idx, row in df.iterrows():
                utt_id = f"Q{idx:08x}_ch0_1.wav"
                ref_text = self.clean_text(row[ref_col])
                f.write(f"{utt_id} {ref_text}\n")
        
        print(f"创建假设文件: {hyp_file}")
        
        # 创建假设文件（原始文本，只清理标点）
        with open(hyp_file, 'w', encoding='utf-8') as f:
            for idx, row in df.iterrows():
                utt_id = f"Q{idx:08x}_ch0_1.wav"
                hyp_text = self.clean_text(row[hyp_col])
                f.write(f"{utt_id} {hyp_text}\n")
        
        print(f"✓ 成功创建文件 ({filtered_len} 行)")
        return ref_file, hyp_file
    
    def calculate_wer(self, ref_file: str, hyp_file: str, verbose: bool = True) -> Optional[str]:
        """
        计算字符级WER
        """
        if not os.path.exists(ref_file):
            print(f"错误: 参考文件不存在: {ref_file}")
            return None
        
        if not os.path.exists(hyp_file):
            print(f"错误: 假设文件不存在: {hyp_file}")
            return None
        
        if not os.path.exists('compute-wer.py'):
            print("错误: compute-wer.py文件不存在")
            return None
        
        # 构建命令 - 使用字符级计算
        cmd = ['python', 'compute-wer.py', '--char=1']
        
        if verbose:
            cmd.append('--v=1')
        else:
            cmd.append('--v=0')
        
        cmd.extend([ref_file, hyp_file])
        
        print(f"执行命令: {' '.join(cmd)}")
        print("=" * 80)
        
        try:
            # 设置环境变量确保正确的编码
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            
            result = subprocess.run(cmd, capture_output=True, text=True, 
                                  encoding='utf-8', errors='replace', env=env)
            
            if result.returncode == 0:
                print("字符级WER计算结果:")
                print("=" * 80)
                print(result.stdout)
                return result.stdout
            else:
                print(f"WER计算失败，返回码: {result.returncode}")
                if result.stderr:
                    print(f"错误信息: {result.stderr}")
                return None
            
        except Exception as e:
            print(f"执行WER计算时发生错误: {e}")
            return None
    
    def extract_overall_wer(self, wer_output: str) -> Optional[float]:
        """从WER输出中提取总体WER值"""
        if not wer_output:
            return None
        
        # 查找Overall WER行
        lines = wer_output.split('\n')
        for line in lines:
            if line.startswith('Overall ->'):
                # 提取WER百分比
                match = re.search(r'(\d+\.\d+)\s*%', line)
                if match:
                    return float(match.group(1))
        return None
    
    def save_results(self, wer_output: str, output_file: str):
        """保存WER结果到文件"""
        if wer_output:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(wer_output)
            print(f"✓ WER结果已保存到: {output_file}")
    
    def generate_summary(self, wer_output: str, total_sentences: int) -> str:
        """生成简单的摘要报告"""
        if not wer_output:
            return "无法生成摘要：WER计算失败"
        
        # 提取统计信息
        overall_wer = self.extract_overall_wer(wer_output)
        
        # 从输出中提取详细统计
        lines = wer_output.split('\n')
        overall_stats = {}
        
        for line in lines:
            if line.startswith('Overall ->'):
                # 解析: Overall -> 7.14 % N=119933 C=113212 S=4410 D=2311 I=1838
                match = re.search(r'Overall -> (\d+\.\d+)\s*%\s*N=(\d+)\s*C=(\d+)\s*S=(\d+)\s*D=(\d+)\s*I=(\d+)', line)
                if match:
                    overall_stats = {
                        'wer': float(match.group(1)),
                        'N': int(match.group(2)),
                        'C': int(match.group(3)),
                        'S': int(match.group(4)),
                        'D': int(match.group(5)),
                        'I': int(match.group(6))
                    }
                break
        
        if not overall_stats:
            return "无法解析WER统计信息"
        
        # 生成摘要
        summary = f"""
中文语音识别WER评估报告
{'='*50}

数据概览:
- 总句子数: {total_sentences:,}
- 总字符数: {overall_stats['N']:,}

WER结果:
- 总体WER: {overall_stats['wer']:.2f}%
- 正确字符: {overall_stats['C']:,} ({overall_stats['C']/overall_stats['N']*100:.1f}%)
- 替换错误: {overall_stats['S']:,} ({overall_stats['S']/overall_stats['N']*100:.1f}%)
- 删除错误: {overall_stats['D']:,} ({overall_stats['D']/overall_stats['N']*100:.1f}%)
- 插入错误: {overall_stats['I']:,} ({overall_stats['I']/overall_stats['N']*100:.1f}%)

质量评估:"""
        
        wer_value = overall_stats['wer']
        if wer_value < 5:
            summary += "\n- 识别质量: 优秀 (WER < 5%)"
        elif wer_value < 10:
            summary += "\n- 识别质量: 良好 (5% ≤ WER < 10%)"
        elif wer_value < 20:
            summary += "\n- 识别质量: 一般 (10% ≤ WER < 20%)"
        else:
            summary += "\n- 识别质量: 需要改进 (WER ≥ 20%)"
        
        summary += f"""

错误分析:
- 平均每句错误字符数: {(overall_stats['S'] + overall_stats['D'] + overall_stats['I']) / total_sentences:.1f}
- 主要错误类型: """
        
        errors = [
            ('替换', overall_stats['S']),
            ('删除', overall_stats['D']),
            ('插入', overall_stats['I'])
        ]
        errors.sort(key=lambda x: x[1], reverse=True)
        summary += f"{errors[0][0]}错误 ({errors[0][1]} 次)"
        
        return summary
    
    def run_analysis(self, excel_file: str, ref_col: str = 'text', 
                    hyp_col: str = '复核后文本', verbose: bool = True):
        """
        运行完整的字符级WER分析
        """
        print("开始中文字符级WER分析")
        print("=" * 80)
        
        # 1. 创建WER文件
        ref_file, hyp_file = self.create_wer_files(excel_file, ref_col, hyp_col)
        
        if not ref_file or not hyp_file:
            print("创建WER文件失败")
            return None
        
        # 2. 计算WER
        wer_output = self.calculate_wer(ref_file, hyp_file, verbose=verbose)
        
        if not wer_output:
            print("WER计算失败")
            return None
        
        # 3. 保存结果
        output_file = os.path.join(self.output_dir, "chinese_char_wer_results.txt")
        self.save_results(wer_output, output_file)
        
        # 4. 生成摘要
        df = pd.read_excel(excel_file)
        df = df.dropna(subset=[ref_col, hyp_col])
        total_sentences = len(df)
        
        summary = self.generate_summary(wer_output, total_sentences)
        
        # 5. 显示摘要
        print("\n" + summary)
        
        # 7. 提取总体WER
        overall_wer = self.extract_overall_wer(wer_output)
        
        print(f"\n{'='*80}")
        print("分析完成！")
        print(f"总体字符级WER: {overall_wer:.2f}%")
        print("生成的文件:")
        print(f"- {output_file} (完整WER结果)")
        print(f"- {ref_file} (参考文件)")
        print(f"- {hyp_file} (假设文件)")
        
        return overall_wer

def main():
    """主函数"""
    excel_file = '360质检系统_文本已复核.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"错误: Excel文件不存在: {excel_file}")
        return
    
    # 创建WER计算器
    calculator = SimpleChineseWERCalculator()
    
    # 运行分析
    overall_wer = calculator.run_analysis(
        excel_file=excel_file,
        ref_col='text',
        hyp_col='复核后文本',
        verbose=True  # 设为False可以只显示总体结果
    )
    
    if overall_wer is not None:
        print(f"\n🎯 最终结果: 字符级WER = {overall_wer:.2f}%")
    else:
        print("\n❌ 分析失败")

if __name__ == '__main__':
    main()