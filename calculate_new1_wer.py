#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re
import sys
import unicodedata
import os

# WER计算相关函数
remove_tag = True
spacelist = [' ', '\t', '\r', '\n']
puncts = ['!', ',', '?', '、', '。', '！', '，', '；', '？',
          '：', '「', '」', '︰', '『', '』', '《', '》']

def characterize(string):
    """将字符串转换为字符列表，过滤标点符号"""
    res = []
    i = 0
    while i < len(string):
        char = string[i]
        if char in puncts:
            i += 1
            continue
        cat1 = unicodedata.category(char)
        if cat1 == 'Zs' or cat1 == 'Cn' or char in spacelist:
            i += 1
            continue
        if cat1 == 'Lo':
            res.append(char)
            i += 1
        else:
            sep = ' '
            if char == '<': sep = '>'
            j = i+1
            while j < len(string):
                c = string[j]
                if ord(c) >= 128 or (c in spacelist) or (c==sep):
                    break
                j += 1
            if j < len(string) and string[j] == '>':
                j += 1
            res.append(string[i:j])
            i = j
    return res

def stripoff_tags(x):
    if not x: return ''
    chars = []
    i = 0; T=len(x)
    while i < T:
        if x[i] == '<':
            while i < T and x[i] != '>':
                i += 1
            i += 1
        else:
            chars.append(x[i])
            i += 1
    return ''.join(chars)

def normalize(sentence, ignore_words, cs, split=None):
    new_sentence = []
    for token in sentence:
        x = token
        if not cs:
            x = x.upper()
        if x in ignore_words:
            continue
        if remove_tag:
            x = stripoff_tags(x)
        if not x:
            continue
        if split and x in split:
            new_sentence += split[x]
        else:
            new_sentence.append(x)
    return new_sentence

class WERCalculator:
    def __init__(self):
        self.data = {}
        self.space = []
        self.cost = {}
        self.cost['cor'] = 0
        self.cost['sub'] = 1
        self.cost['del'] = 1
        self.cost['ins'] = 1
    
    def calculate(self, lab, rec):
        # 初始化
        lab.insert(0, '')
        rec.insert(0, '')
        while len(self.space) < len(lab):
            self.space.append([])
        for row in self.space:
            for element in row:
                element['dist'] = 0
                element['error'] = 'non'
            while len(row) < len(rec):
                row.append({'dist': 0, 'error': 'non'})
        
        for i in range(len(lab)):
            self.space[i][0]['dist'] = i
            self.space[i][0]['error'] = 'del'
        for j in range(len(rec)):
            self.space[0][j]['dist'] = j
            self.space[0][j]['error'] = 'ins'
        self.space[0][0]['error'] = 'non'
        
        for token in lab:
            if token not in self.data and len(token) > 0:
                self.data[token] = {'all': 0, 'cor': 0, 'sub': 0, 'ins': 0, 'del': 0}
        for token in rec:
            if token not in self.data and len(token) > 0:
                self.data[token] = {'all': 0, 'cor': 0, 'sub': 0, 'ins': 0, 'del': 0}
        
        # 计算编辑距离
        for i, lab_token in enumerate(lab):
            for j, rec_token in enumerate(rec):
                if i == 0 or j == 0:
                    continue
                min_dist = sys.maxsize
                min_error = 'none'
                
                # 删除
                dist = self.space[i-1][j]['dist'] + self.cost['del']
                error = 'del'
                if dist < min_dist:
                    min_dist = dist
                    min_error = error
                
                # 插入
                dist = self.space[i][j-1]['dist'] + self.cost['ins']
                error = 'ins'
                if dist < min_dist:
                    min_dist = dist
                    min_error = error
                
                # 正确或替换
                if lab_token == rec_token:
                    dist = self.space[i-1][j-1]['dist'] + self.cost['cor']
                    error = 'cor'
                else:
                    dist = self.space[i-1][j-1]['dist'] + self.cost['sub']
                    error = 'sub'
                if dist < min_dist:
                    min_dist = dist
                    min_error = error
                
                self.space[i][j]['dist'] = min_dist
                self.space[i][j]['error'] = min_error
        
        # 回溯
        result = {'lab': [], 'rec': [], 'all': 0, 'cor': 0, 'sub': 0, 'ins': 0, 'del': 0}
        i = len(lab) - 1
        j = len(rec) - 1
        
        while True:
            if self.space[i][j]['error'] == 'cor':  # 正确
                if len(lab[i]) > 0:
                    self.data[lab[i]]['all'] = self.data[lab[i]]['all'] + 1
                    self.data[lab[i]]['cor'] = self.data[lab[i]]['cor'] + 1
                    result['all'] = result['all'] + 1
                    result['cor'] = result['cor'] + 1
                result['lab'].insert(0, lab[i])
                result['rec'].insert(0, rec[j])
                i = i - 1
                j = j - 1
            elif self.space[i][j]['error'] == 'sub':  # 替换
                if len(lab[i]) > 0:
                    self.data[lab[i]]['all'] = self.data[lab[i]]['all'] + 1
                    self.data[lab[i]]['sub'] = self.data[lab[i]]['sub'] + 1
                    result['all'] = result['all'] + 1
                    result['sub'] = result['sub'] + 1
                result['lab'].insert(0, lab[i])
                result['rec'].insert(0, rec[j])
                i = i - 1
                j = j - 1
            elif self.space[i][j]['error'] == 'del':  # 删除
                if len(lab[i]) > 0:
                    self.data[lab[i]]['all'] = self.data[lab[i]]['all'] + 1
                    self.data[lab[i]]['del'] = self.data[lab[i]]['del'] + 1
                    result['all'] = result['all'] + 1
                    result['del'] = result['del'] + 1
                result['lab'].insert(0, lab[i])
                result['rec'].insert(0, "")
                i = i - 1
            elif self.space[i][j]['error'] == 'ins':  # 插入
                if len(rec[j]) > 0:
                    self.data[rec[j]]['ins'] = self.data[rec[j]]['ins'] + 1
                    result['ins'] = result['ins'] + 1
                result['lab'].insert(0, "")
                result['rec'].insert(0, rec[j])
                j = j - 1
            elif self.space[i][j]['error'] == 'non':  # 起始点
                break
            else:
                print(f'this should not happen, i = {i}, j = {j}, error = {self.space[i][j]["error"]}')
        
        return result
    
    def overall(self):
        result = {'all': 0, 'cor': 0, 'sub': 0, 'ins': 0, 'del': 0}
        for token in self.data:
            result['all'] = result['all'] + self.data[token]['all']
            result['cor'] = result['cor'] + self.data[token]['cor']
            result['sub'] = result['sub'] + self.data[token]['sub']
            result['ins'] = result['ins'] + self.data[token]['ins']
            result['del'] = result['del'] + self.data[token]['del']
        return result

def clean_text(text):
    """清理文本，移除说话人标签和无用字符"""
    if pd.isna(text) or text is None:
        return ""
    
    text = str(text).strip()
    
    # 按行分割
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # 移除说话人标签（冒号之前的内容）
        if '：' in line:
            # 找到冒号，取冒号后的内容
            parts = line.split('：', 1)
            if len(parts) > 1:
                content = parts[1].strip()
                if content:
                    cleaned_lines.append(content)
        else:
            # 如果没有冒号，直接添加
            cleaned_lines.append(line)
    
    # 合并所有行
    result = ' '.join(cleaned_lines)
    
    # 移除特殊标记
    result = result.replace('***', '').replace('（空）', '').replace('(空)', '')
    
    return result.strip()

def calculate_wer_for_excel(excel_file):
    """计算Excel文件中D列和E列的WER"""
    
    print("=" * 80)
    print("New1.xlsx WER分析")
    print("=" * 80)
    
    # 读取Excel文件
    try:
        df = pd.read_excel(excel_file)
        print(f"成功读取Excel文件，共 {len(df)} 行数据")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return None
    
    # 获取D列和E列
    asr_col = df.iloc[:, 3]  # D列 (ASR结果)
    label_col = df.iloc[:, 4]  # E列 (标注结果)
    
    print(f"D列名称: {df.columns[3]}")
    print(f"E列名称: {df.columns[4]}")
    
    calculator = WERCalculator()
    ignore_words = set()
    case_sensitive = False
    
    valid_pairs = 0
    individual_results = []
    
    # 逐行计算WER
    for idx in range(len(df)):
        asr_text = asr_col.iloc[idx]
        label_text = label_col.iloc[idx]
        
        # 跳过空值
        if pd.isna(asr_text) or pd.isna(label_text):
            continue
        
        # 清理文本
        asr_cleaned = clean_text(asr_text)
        label_cleaned = clean_text(label_text)
        
        if not asr_cleaned or not label_cleaned:
            continue
        
        valid_pairs += 1
        
        print(f"\n--- 第 {idx+1} 行 ---")
        print(f"ASR原文: {str(asr_text)[:100]}...")
        print(f"标注原文: {str(label_text)[:100]}...")
        print(f"ASR清理后: {asr_cleaned[:100]}...")
        print(f"标注清理后: {label_cleaned[:100]}...")
        
        # 转换为字符列表
        asr_chars = characterize(asr_cleaned)
        label_chars = characterize(label_cleaned)
        
        # 标准化处理
        asr_normalized = normalize(asr_chars, ignore_words, case_sensitive)
        label_normalized = normalize(label_chars, ignore_words, case_sensitive)
        
        print(f"ASR字符数: {len(asr_normalized)}")
        print(f"标注字符数: {len(label_normalized)}")
        
        # 计算WER
        result = calculator.calculate(label_normalized.copy(), asr_normalized.copy())
        
        if result['all'] != 0:
            wer = float(result['ins'] + result['sub'] + result['del']) * 100.0 / result['all']
        else:
            wer = 0.0
        
        individual_results.append({
            'row': idx + 1,
            'wer': wer,
            'result': result
        })
        
        print(f'WER: {wer:4.2f}% (N={result["all"]} C={result["cor"]} S={result["sub"]} D={result["del"]} I={result["ins"]})')
    
    # 计算总体WER
    print("\n" + "=" * 80)
    print("总体结果")
    print("=" * 80)
    
    overall_result = calculator.overall()
    if overall_result['all'] != 0:
        overall_wer = float(overall_result['ins'] + overall_result['sub'] + overall_result['del']) * 100.0 / overall_result['all']
    else:
        overall_wer = 0.0
    
    print(f'处理的有效行数: {valid_pairs}')
    print(f'总体WER: {overall_wer:4.2f}%')
    print(f'总字符数: {overall_result["all"]:,}')
    print(f'正确字符: {overall_result["cor"]:,} ({overall_result["cor"]/overall_result["all"]*100:.1f}%)')
    print(f'替换错误: {overall_result["sub"]:,} ({overall_result["sub"]/overall_result["all"]*100:.1f}%)')
    print(f'删除错误: {overall_result["del"]:,} ({overall_result["del"]/overall_result["all"]*100:.1f}%)')
    print(f'插入错误: {overall_result["ins"]:,} ({overall_result["ins"]/overall_result["all"]*100:.1f}%)')
    
    # 保存详细结果
    output_file = "new1_wer_results.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("New1.xlsx WER分析结果\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("各行WER结果:\n")
        for result_info in individual_results:
            result = result_info['result']
            f.write(f"第{result_info['row']}行: WER={result_info['wer']:4.2f}% ")
            f.write(f"(N={result['all']} C={result['cor']} S={result['sub']} D={result['del']} I={result['ins']})\n")
        
        f.write(f"\n总体结果:\n")
        f.write(f"处理的有效行数: {valid_pairs}\n")
        f.write(f"总体WER: {overall_wer:4.2f}%\n")
        f.write(f"总字符数: {overall_result['all']:,}\n")
        f.write(f"正确: {overall_result['cor']:,}, 替换: {overall_result['sub']:,}, 删除: {overall_result['del']:,}, 插入: {overall_result['ins']:,}\n")
    
    print(f"\n详细结果已保存到: {output_file}")
    
    return overall_wer, overall_result

if __name__ == "__main__":
    excel_file = "new1.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"错误: Excel文件不存在: {excel_file}")
        sys.exit(1)
    
    wer, result = calculate_wer_for_excel(excel_file)