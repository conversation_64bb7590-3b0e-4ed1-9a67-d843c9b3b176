#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import sys
import unicodedata
import os

# 从compute_wer.py复制必要的函数和类
remove_tag = True
spacelist= [' ', '\t', '\r', '\n']
puncts = ['!', ',', '?', '、', '。', '！', '，', '；', '？',
          '：', '「', '」', '︰',  '『', '』', '《', '》']

def characterize(string):
    """将字符串转换为字符列表，过滤标点符号"""
    res = []
    i = 0
    while i < len(string):
        char = string[i]
        if char in puncts:
            i += 1
            continue
        cat1 = unicodedata.category(char)
        if cat1 == 'Zs' or cat1 == 'Cn' or char in spacelist:
            i += 1
            continue
        if cat1 == 'Lo':
            res.append(char)
            i += 1
        else:
            sep = ' '
            if char == '<': sep = '>'
            j = i+1
            while j < len(string):
                c = string[j]
                if ord(c) >= 128 or (c in spacelist) or (c==sep):
                    break
                j += 1
            if j < len(string) and string[j] == '>':
                j += 1
            res.append(string[i:j])
            i = j
    return res

def stripoff_tags(x):
    if not x: return ''
    chars = []
    i = 0; T=len(x)
    while i < T:
        if x[i] == '<':
            while i < T and x[i] != '>':
                i += 1
            i += 1
        else:
            chars.append(x[i])
            i += 1
    return ''.join(chars)

def normalize(sentence, ignore_words, cs, split=None):
    new_sentence = []
    for token in sentence:
        x = token
        if not cs:
            x = x.upper()
        if x in ignore_words:
            continue
        if remove_tag:
            x = stripoff_tags(x)
        if not x:
            continue
        if split and x in split:
            new_sentence += split[x]
        else:
            new_sentence.append(x)
    return new_sentence

def width(string):
    return sum(1 + (unicodedata.east_asian_width(c) in "AFW") for c in string)

class DetailedCalculator:
    def __init__(self):
        self.data = {}
        self.space = []
        self.cost = {}
        self.cost['cor'] = 0
        self.cost['sub'] = 1
        self.cost['del'] = 1
        self.cost['ins'] = 1
    
    def calculate(self, lab, rec):
        # 保存原始输入用于显示
        original_lab = lab.copy()
        original_rec = rec.copy()
        
        # 初始化
        lab.insert(0, '')
        rec.insert(0, '')
        while len(self.space) < len(lab):
            self.space.append([])
        for row in self.space:
            for element in row:
                element['dist'] = 0
                element['error'] = 'non'
            while len(row) < len(rec):
                row.append({'dist': 0, 'error': 'non'})
        
        for i in range(len(lab)):
            self.space[i][0]['dist'] = i
            self.space[i][0]['error'] = 'del'
        for j in range(len(rec)):
            self.space[0][j]['dist'] = j
            self.space[0][j]['error'] = 'ins'
        self.space[0][0]['error'] = 'non'
        
        for token in lab:
            if token not in self.data and len(token) > 0:
                self.data[token] = {'all': 0, 'cor': 0, 'sub': 0, 'ins': 0, 'del': 0}
        for token in rec:
            if token not in self.data and len(token) > 0:
                self.data[token] = {'all': 0, 'cor': 0, 'sub': 0, 'ins': 0, 'del': 0}
        
        # 计算编辑距离
        for i, lab_token in enumerate(lab):
            for j, rec_token in enumerate(rec):
                if i == 0 or j == 0:
                    continue
                min_dist = sys.maxsize
                min_error = 'none'
                
                # 删除
                dist = self.space[i-1][j]['dist'] + self.cost['del']
                error = 'del'
                if dist < min_dist:
                    min_dist = dist
                    min_error = error
                
                # 插入
                dist = self.space[i][j-1]['dist'] + self.cost['ins']
                error = 'ins'
                if dist < min_dist:
                    min_dist = dist
                    min_error = error
                
                # 正确或替换
                if lab_token == rec_token:
                    dist = self.space[i-1][j-1]['dist'] + self.cost['cor']
                    error = 'cor'
                else:
                    dist = self.space[i-1][j-1]['dist'] + self.cost['sub']
                    error = 'sub'
                if dist < min_dist:
                    min_dist = dist
                    min_error = error
                
                self.space[i][j]['dist'] = min_dist
                self.space[i][j]['error'] = min_error
        
        # 回溯
        result = {'lab': [], 'rec': [], 'all': 0, 'cor': 0, 'sub': 0, 'ins': 0, 'del': 0}
        i = len(lab) - 1
        j = len(rec) - 1
        
        while True:
            if self.space[i][j]['error'] == 'cor':  # 正确
                if len(lab[i]) > 0:
                    self.data[lab[i]]['all'] = self.data[lab[i]]['all'] + 1
                    self.data[lab[i]]['cor'] = self.data[lab[i]]['cor'] + 1
                    result['all'] = result['all'] + 1
                    result['cor'] = result['cor'] + 1
                result['lab'].insert(0, lab[i])
                result['rec'].insert(0, rec[j])
                i = i - 1
                j = j - 1
            elif self.space[i][j]['error'] == 'sub':  # 替换
                if len(lab[i]) > 0:
                    self.data[lab[i]]['all'] = self.data[lab[i]]['all'] + 1
                    self.data[lab[i]]['sub'] = self.data[lab[i]]['sub'] + 1
                    result['all'] = result['all'] + 1
                    result['sub'] = result['sub'] + 1
                result['lab'].insert(0, lab[i])
                result['rec'].insert(0, rec[j])
                i = i - 1
                j = j - 1
            elif self.space[i][j]['error'] == 'del':  # 删除
                if len(lab[i]) > 0:
                    self.data[lab[i]]['all'] = self.data[lab[i]]['all'] + 1
                    self.data[lab[i]]['del'] = self.data[lab[i]]['del'] + 1
                    result['all'] = result['all'] + 1
                    result['del'] = result['del'] + 1
                result['lab'].insert(0, lab[i])
                result['rec'].insert(0, "")
                i = i - 1
            elif self.space[i][j]['error'] == 'ins':  # 插入
                if len(rec[j]) > 0:
                    self.data[rec[j]]['ins'] = self.data[rec[j]]['ins'] + 1
                    result['ins'] = result['ins'] + 1
                result['lab'].insert(0, "")
                result['rec'].insert(0, rec[j])
                j = j - 1
            elif self.space[i][j]['error'] == 'non':  # 起始点
                break
            else:
                print(f'this should not happen, i = {i}, j = {j}, error = {self.space[i][j]["error"]}')
        
        return result
    
    def overall(self):
        result = {'all': 0, 'cor': 0, 'sub': 0, 'ins': 0, 'del': 0}
        for token in self.data:
            result['all'] = result['all'] + self.data[token]['all']
            result['cor'] = result['cor'] + self.data[token]['cor']
            result['sub'] = result['sub'] + self.data[token]['sub']
            result['ins'] = result['ins'] + self.data[token]['ins']
            result['del'] = result['del'] + self.data[token]['del']
        return result

def read_dialogue_file(filename):
    """读取对话文件并返回对话列表"""
    dialogues = []
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read().strip()
        dialogue_blocks = content.split('\n\n')
        for block in dialogue_blocks:
            if block.strip():
                if '：' in block:
                    dialogue_text = block.split('：', 1)[1].strip()
                    dialogues.append(dialogue_text)
    return dialogues

def write_alignment_to_file(f, result, dialogue_id, max_chars_per_line=80):
    """将对齐结果写入文件"""
    f.write(f"字符级对比:\n")
    
    # 计算每个字符的显示宽度
    space = {'lab': [], 'rec': []}
    for idx in range(len(result['lab'])):
        len_lab = width(result['lab'][idx]) if result['lab'][idx] else 1
        len_rec = width(result['rec'][idx]) if result['rec'][idx] else 1
        length = max(len_lab, len_rec, 1)
        space['lab'].append(length - len_lab)
        space['rec'].append(length - len_rec)
    
    # 分行显示
    upper_lab = len(result['lab'])
    upper_rec = len(result['rec'])
    lab1, rec1 = 0, 0
    
    while lab1 < upper_lab or rec1 < upper_rec:
        # 计算这一行能显示多少字符
        current_width = 0
        lab2 = lab1
        while lab2 < upper_lab and current_width < max_chars_per_line:
            char_width = width(result['lab'][lab2]) if result['lab'][lab2] else 1
            if current_width + char_width + space['lab'][lab2] + 1 > max_chars_per_line:
                break
            current_width += char_width + space['lab'][lab2] + 1
            lab2 += 1
        
        rec2 = min(upper_rec, rec1 + (lab2 - lab1))
        
        # 写入参考文本行
        f.write('参考: ')
        for idx in range(lab1, lab2):
            token = result['lab'][idx] if result['lab'][idx] else '_'
            f.write(f'{token}')
            for n in range(space['lab'][idx]):
                f.write(' ')
            f.write(' ')
        f.write('\n')
        
        # 写入识别文本行
        f.write('识别: ')
        for idx in range(rec1, rec2):
            token = result['rec'][idx] if result['rec'][idx] else '_'
            f.write(f'{token}')
            for n in range(space['rec'][idx]):
                f.write(' ')
            f.write(' ')
        f.write('\n')
        
        # 写入错误标记行
        f.write('标记: ')
        for idx in range(lab1, lab2):
            lab_token = result['lab'][idx]
            rec_token = result['rec'][idx] if idx < len(result['rec']) else ""
            
            if not lab_token and rec_token:  # 插入
                mark = 'I'
            elif lab_token and not rec_token:  # 删除
                mark = 'D'
            elif lab_token != rec_token:  # 替换
                mark = 'S'
            else:  # 正确
                mark = ' '
            
            f.write(f'{mark}')
            for n in range(space['lab'][idx]):
                f.write(' ')
            f.write(' ')
        f.write('\n\n')
        
        lab1 = lab2
        rec1 = rec2

def print_alignment(result, dialogue_id, max_chars_per_line=80):
    """打印对齐结果，显示替换、删除、插入"""
    print(f"\n=== 对话 {dialogue_id} 详细对比 ===")
    
    # 计算每个字符的显示宽度
    space = {'lab': [], 'rec': []}
    for idx in range(len(result['lab'])):
        len_lab = width(result['lab'][idx]) if result['lab'][idx] else 1
        len_rec = width(result['rec'][idx]) if result['rec'][idx] else 1
        length = max(len_lab, len_rec, 1)
        space['lab'].append(length - len_lab)
        space['rec'].append(length - len_rec)
    
    # 分行显示
    upper_lab = len(result['lab'])
    upper_rec = len(result['rec'])
    lab1, rec1 = 0, 0
    
    while lab1 < upper_lab or rec1 < upper_rec:
        # 计算这一行能显示多少字符
        current_width = 0
        lab2 = lab1
        while lab2 < upper_lab and current_width < max_chars_per_line:
            char_width = width(result['lab'][lab2]) if result['lab'][lab2] else 1
            if current_width + char_width + space['lab'][lab2] + 1 > max_chars_per_line:
                break
            current_width += char_width + space['lab'][lab2] + 1
            lab2 += 1
        
        rec2 = min(upper_rec, rec1 + (lab2 - lab1))
        
        # 打印参考文本行
        print('参考: ', end='')
        for idx in range(lab1, lab2):
            token = result['lab'][idx] if result['lab'][idx] else '_'
            print(f'{token}', end='')
            for n in range(space['lab'][idx]):
                print(' ', end='')
            print(' ', end='')
        print()
        
        # 打印识别文本行
        print('识别: ', end='')
        for idx in range(rec1, rec2):
            token = result['rec'][idx] if result['rec'][idx] else '_'
            print(f'{token}', end='')
            for n in range(space['rec'][idx]):
                print(' ', end='')
            print(' ', end='')
        print()
        
        # 打印错误标记行
        print('标记: ', end='')
        for idx in range(lab1, lab2):
            lab_token = result['lab'][idx]
            rec_token = result['rec'][idx] if idx < len(result['rec']) else ""
            
            if not lab_token and rec_token:  # 插入
                mark = 'I'
            elif lab_token and not rec_token:  # 删除
                mark = 'D'
            elif lab_token != rec_token:  # 替换
                mark = 'S'
            else:  # 正确
                mark = ' '
            
            print(f'{mark}', end='')
            for n in range(space['lab'][idx]):
                print(' ', end='')
            print(' ', end='')
        print()
        print()  # 空行分隔
        
        lab1 = lab2
        rec1 = rec2

def analyze_errors(result):
    """分析错误类型并统计"""
    errors = {'substitutions': [], 'deletions': [], 'insertions': []}
    
    for i in range(len(result['lab'])):
        lab_token = result['lab'][i]
        rec_token = result['rec'][i] if i < len(result['rec']) else ""
        
        if not lab_token and rec_token:  # 插入
            errors['insertions'].append(rec_token)
        elif lab_token and not rec_token:  # 删除
            errors['deletions'].append(lab_token)
        elif lab_token != rec_token and lab_token and rec_token:  # 替换
            errors['substitutions'].append((lab_token, rec_token))
    
    return errors

def calculate_detailed_wer(ref_file, hyp_file):
    """计算详细的WER，包括每句话的对比"""
    
    print("=" * 80)
    print("详细对话WER分析")
    print("=" * 80)
    
    # 读取对话文件
    ref_dialogues = read_dialogue_file(ref_file)
    hyp_dialogues = read_dialogue_file(hyp_file)
    
    print(f"参考文件 ({ref_file}) 包含 {len(ref_dialogues)} 个对话")
    print(f"识别文件 ({hyp_file}) 包含 {len(hyp_dialogues)} 个对话")
    
    min_dialogues = min(len(ref_dialogues), len(hyp_dialogues))
    
    calculator = DetailedCalculator()
    ignore_words = set()
    case_sensitive = False
    
    all_results = []
    
    # 逐个对话计算WER
    for i in range(min_dialogues):
        print(f"\n{'='*60}")
        print(f"对话 {i+1}")
        print(f"{'='*60}")
        
        # 转换为字符列表
        ref_chars = characterize(ref_dialogues[i])
        hyp_chars = characterize(hyp_dialogues[i])
        
        # 标准化处理
        ref_normalized = normalize(ref_chars, ignore_words, case_sensitive)
        hyp_normalized = normalize(hyp_chars, ignore_words, case_sensitive)
        
        print(f"参考文本长度: {len(ref_normalized)} 字符")
        print(f"识别文本长度: {len(hyp_normalized)} 字符")
        
        # 计算WER
        result = calculator.calculate(ref_normalized.copy(), hyp_normalized.copy())
        all_results.append(result)
        
        if result['all'] != 0:
            wer = float(result['ins'] + result['sub'] + result['del']) * 100.0 / result['all']
        else:
            wer = 0.0
        
        print(f'WER: {wer:4.2f}% (N={result["all"]} C={result["cor"]} S={result["sub"]} D={result["del"]} I={result["ins"]})')
        
        # 显示详细对比
        print_alignment(result, i+1)
        
        # 分析错误
        errors = analyze_errors(result)
        
        if errors['substitutions']:
            print("替换错误:")
            for orig, wrong in errors['substitutions'][:10]:  # 只显示前10个
                print(f"  '{orig}' → '{wrong}'")
            if len(errors['substitutions']) > 10:
                print(f"  ... 还有 {len(errors['substitutions']) - 10} 个替换错误")
        
        if errors['deletions']:
            print("删除错误:")
            for deleted in errors['deletions'][:10]:  # 只显示前10个
                print(f"  缺少: '{deleted}'")
            if len(errors['deletions']) > 10:
                print(f"  ... 还有 {len(errors['deletions']) - 10} 个删除错误")
        
        if errors['insertions']:
            print("插入错误:")
            for inserted in errors['insertions'][:10]:  # 只显示前10个
                print(f"  多余: '{inserted}'")
            if len(errors['insertions']) > 10:
                print(f"  ... 还有 {len(errors['insertions']) - 10} 个插入错误")
    
    # 计算总体WER
    print("\n" + "=" * 80)
    print("总体结果")
    print("=" * 80)
    
    overall_result = calculator.overall()
    if overall_result['all'] != 0:
        overall_wer = float(overall_result['ins'] + overall_result['sub'] + overall_result['del']) * 100.0 / overall_result['all']
    else:
        overall_wer = 0.0
    
    print(f'总体WER: {overall_wer:4.2f}%')
    print(f'总字符数: {overall_result["all"]:,}')
    print(f'正确字符: {overall_result["cor"]:,} ({overall_result["cor"]/overall_result["all"]*100:.1f}%)')
    print(f'替换错误: {overall_result["sub"]:,} ({overall_result["sub"]/overall_result["all"]*100:.1f}%)')
    print(f'删除错误: {overall_result["del"]:,} ({overall_result["del"]/overall_result["all"]*100:.1f}%)')
    print(f'插入错误: {overall_result["ins"]:,} ({overall_result["ins"]/overall_result["all"]*100:.1f}%)')
    
    # 保存详细结果到文件
    output_file = "detailed_wer_results.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("详细WER分析结果\n")
        f.write("=" * 80 + "\n\n")
        
        f.write(f"参考文件: {ref_file}\n")
        f.write(f"识别文件: {hyp_file}\n")
        f.write(f"对话数量: {min_dialogues}\n\n")
        
        # 写入每个对话的详细分析
        for i, result in enumerate(all_results):
            if result['all'] != 0:
                wer = float(result['ins'] + result['sub'] + result['del']) * 100.0 / result['all']
            else:
                wer = 0.0
            
            f.write("=" * 60 + "\n")
            f.write(f"对话 {i+1} 详细分析\n")
            f.write("=" * 60 + "\n")
            f.write(f"WER: {wer:4.2f}% (N={result['all']} C={result['cor']} S={result['sub']} D={result['del']} I={result['ins']})\n\n")
            
            # 写入对齐结果
            write_alignment_to_file(f, result, i+1)
            
            # 写入错误分析
            errors = analyze_errors(result)
            
            if errors['substitutions']:
                f.write("替换错误:\n")
                for orig, wrong in errors['substitutions']:
                    f.write(f"  '{orig}' → '{wrong}'\n")
                f.write("\n")
            
            if errors['deletions']:
                f.write("删除错误:\n")
                for deleted in errors['deletions']:
                    f.write(f"  缺少: '{deleted}'\n")
                f.write("\n")
            
            if errors['insertions']:
                f.write("插入错误:\n")
                for inserted in errors['insertions']:
                    f.write(f"  多余: '{inserted}'\n")
                f.write("\n")
        
        # 写入总体结果
        f.write("=" * 80 + "\n")
        f.write("总体结果\n")
        f.write("=" * 80 + "\n")
        f.write(f"总体WER: {overall_wer:4.2f}%\n")
        f.write(f"总字符数: {overall_result['all']:,}\n")
        f.write(f"正确字符: {overall_result['cor']:,} ({overall_result['cor']/overall_result['all']*100:.1f}%)\n")
        f.write(f"替换错误: {overall_result['sub']:,} ({overall_result['sub']/overall_result['all']*100:.1f}%)\n")
        f.write(f"删除错误: {overall_result['del']:,} ({overall_result['del']/overall_result['all']*100:.1f}%)\n")
        f.write(f"插入错误: {overall_result['ins']:,} ({overall_result['ins']/overall_result['all']*100:.1f}%)\n\n")
        
        # 写入质量评估
        f.write("质量评估:\n")
        if overall_wer < 5:
            f.write("- 识别质量: 优秀 (WER < 5%)\n")
        elif overall_wer < 10:
            f.write("- 识别质量: 良好 (5% ≤ WER < 10%)\n")
        elif overall_wer < 20:
            f.write("- 识别质量: 一般 (10% ≤ WER < 20%)\n")
        else:
            f.write("- 识别质量: 需要改进 (WER ≥ 20%)\n")
        
        f.write(f"- 平均每句错误字符数: {(overall_result['sub'] + overall_result['del'] + overall_result['ins']) / min_dialogues:.1f}\n")
        
        errors = [
            ('替换', overall_result['sub']),
            ('删除', overall_result['del']),
            ('插入', overall_result['ins'])
        ]
        errors.sort(key=lambda x: x[1], reverse=True)
        f.write(f"- 主要错误类型: {errors[0][0]}错误 ({errors[0][1]} 次)\n")
    
    print(f"\n详细结果已保存到: {output_file}")
    
    return overall_wer, overall_result

if __name__ == "__main__":
    # 计算WER，dialogue_texts2.txt作为参考（答案），dialogue_texts.txt作为识别结果
    ref_file = "dialogue_texts2.txt"  # 参考答案
    hyp_file = "dialogue_texts.txt"   # 识别结果
    
    if not os.path.exists(ref_file):
        print(f"错误: 参考文件不存在: {ref_file}")
        sys.exit(1)
    
    if not os.path.exists(hyp_file):
        print(f"错误: 识别文件不存在: {hyp_file}")
        sys.exit(1)
    
    wer, result = calculate_detailed_wer(ref_file, hyp_file)