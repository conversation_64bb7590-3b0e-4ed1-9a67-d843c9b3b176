#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re
import sys
import unicodedata
import os
import glob
from pathlib import Path

# WER计算相关函数
remove_tag = True
spacelist = [' ', '\t', '\r', '\n']
puncts = ['!', ',', '?', '、', '。', '！', '，', '；', '？',
          '：', '「', '」', '︰', '『', '』', '《', '》']

def characterize(string):
    """将字符串转换为字符列表，过滤标点符号"""
    res = []
    i = 0
    while i < len(string):
        char = string[i]
        if char in puncts:
            i += 1
            continue
        cat1 = unicodedata.category(char)
        if cat1 == 'Zs' or cat1 == 'Cn' or char in spacelist:
            i += 1
            continue
        if cat1 == 'Lo':
            res.append(char)
            i += 1
        else:
            sep = ' '
            if char == '<': sep = '>'
            j = i+1
            while j < len(string):
                c = string[j]
                if ord(c) >= 128 or (c in spacelist) or (c==sep):
                    break
                j += 1
            if j < len(string) and string[j] == '>':
                j += 1
            res.append(string[i:j])
            i = j
    return res

def stripoff_tags(x):
    if not x: return ''
    chars = []
    i = 0; T=len(x)
    while i < T:
        if x[i] == '<':
            while i < T and x[i] != '>':
                i += 1
            i += 1
        else:
            chars.append(x[i])
            i += 1
    return ''.join(chars)

def normalize(sentence, ignore_words, cs, split=None):
    new_sentence = []
    for token in sentence:
        x = token
        if not cs:
            x = x.upper()
        if x in ignore_words:
            continue
        if remove_tag:
            x = stripoff_tags(x)
        if not x:
            continue
        if split and x in split:
            new_sentence += split[x]
        else:
            new_sentence.append(x)
    return new_sentence

class WERCalculator:
    def __init__(self):
        self.data = {}
        self.space = []
        self.cost = {}
        self.cost['cor'] = 0
        self.cost['sub'] = 1
        self.cost['del'] = 1
        self.cost['ins'] = 1
    
    def calculate(self, lab, rec):
        # 初始化
        lab.insert(0, '')
        rec.insert(0, '')
        while len(self.space) < len(lab):
            self.space.append([])
        for row in self.space:
            for element in row:
                element['dist'] = 0
                element['error'] = 'non'
            while len(row) < len(rec):
                row.append({'dist': 0, 'error': 'non'})
        
        for i in range(len(lab)):
            self.space[i][0]['dist'] = i
            self.space[i][0]['error'] = 'del'
        for j in range(len(rec)):
            self.space[0][j]['dist'] = j
            self.space[0][j]['error'] = 'ins'
        self.space[0][0]['error'] = 'non'
        
        for token in lab:
            if token not in self.data and len(token) > 0:
                self.data[token] = {'all': 0, 'cor': 0, 'sub': 0, 'ins': 0, 'del': 0}
        for token in rec:
            if token not in self.data and len(token) > 0:
                self.data[token] = {'all': 0, 'cor': 0, 'sub': 0, 'ins': 0, 'del': 0}
        
        # 计算编辑距离
        for i, lab_token in enumerate(lab):
            for j, rec_token in enumerate(rec):
                if i == 0 or j == 0:
                    continue
                min_dist = sys.maxsize
                min_error = 'none'
                
                # 删除
                dist = self.space[i-1][j]['dist'] + self.cost['del']
                error = 'del'
                if dist < min_dist:
                    min_dist = dist
                    min_error = error
                
                # 插入
                dist = self.space[i][j-1]['dist'] + self.cost['ins']
                error = 'ins'
                if dist < min_dist:
                    min_dist = dist
                    min_error = error
                
                # 正确或替换
                if lab_token == rec_token:
                    dist = self.space[i-1][j-1]['dist'] + self.cost['cor']
                    error = 'cor'
                else:
                    dist = self.space[i-1][j-1]['dist'] + self.cost['sub']
                    error = 'sub'
                if dist < min_dist:
                    min_dist = dist
                    min_error = error
                
                self.space[i][j]['dist'] = min_dist
                self.space[i][j]['error'] = min_error
        
        # 回溯
        result = {'lab': [], 'rec': [], 'all': 0, 'cor': 0, 'sub': 0, 'ins': 0, 'del': 0, 'alignment': []}
        i = len(lab) - 1
        j = len(rec) - 1
        
        while True:
            if self.space[i][j]['error'] == 'cor':  # 正确
                if len(lab[i]) > 0:
                    self.data[lab[i]]['all'] = self.data[lab[i]]['all'] + 1
                    self.data[lab[i]]['cor'] = self.data[lab[i]]['cor'] + 1
                    result['all'] = result['all'] + 1
                    result['cor'] = result['cor'] + 1
                result['lab'].insert(0, lab[i])
                result['rec'].insert(0, rec[j])
                result['alignment'].insert(0, {'ref': lab[i], 'hyp': rec[j], 'op': 'C'})
                i = i - 1
                j = j - 1
            elif self.space[i][j]['error'] == 'sub':  # 替换
                if len(lab[i]) > 0:
                    self.data[lab[i]]['all'] = self.data[lab[i]]['all'] + 1
                    self.data[lab[i]]['sub'] = self.data[lab[i]]['sub'] + 1
                    result['all'] = result['all'] + 1
                    result['sub'] = result['sub'] + 1
                result['lab'].insert(0, lab[i])
                result['rec'].insert(0, rec[j])
                result['alignment'].insert(0, {'ref': lab[i], 'hyp': rec[j], 'op': 'S'})
                i = i - 1
                j = j - 1
            elif self.space[i][j]['error'] == 'del':  # 删除
                if len(lab[i]) > 0:
                    self.data[lab[i]]['all'] = self.data[lab[i]]['all'] + 1
                    self.data[lab[i]]['del'] = self.data[lab[i]]['del'] + 1
                    result['all'] = result['all'] + 1
                    result['del'] = result['del'] + 1
                result['lab'].insert(0, lab[i])
                result['rec'].insert(0, "")
                result['alignment'].insert(0, {'ref': lab[i], 'hyp': '*', 'op': 'D'})
                i = i - 1
            elif self.space[i][j]['error'] == 'ins':  # 插入
                if len(rec[j]) > 0:
                    self.data[rec[j]]['ins'] = self.data[rec[j]]['ins'] + 1
                    result['ins'] = result['ins'] + 1
                result['lab'].insert(0, "")
                result['rec'].insert(0, rec[j])
                result['alignment'].insert(0, {'ref': '*', 'hyp': rec[j], 'op': 'I'})
                j = j - 1
            elif self.space[i][j]['error'] == 'non':  # 起始点
                break
            else:
                print(f'this should not happen, i = {i}, j = {j}, error = {self.space[i][j]["error"]}')
        
        return result
    
    def overall(self):
        result = {'all': 0, 'cor': 0, 'sub': 0, 'ins': 0, 'del': 0}
        for token in self.data:
            result['all'] = result['all'] + self.data[token]['all']
            result['cor'] = result['cor'] + self.data[token]['cor']
            result['sub'] = result['sub'] + self.data[token]['sub']
            result['ins'] = result['ins'] + self.data[token]['ins']
            result['del'] = result['del'] + self.data[token]['del']
        return result

def read_transcript_file(file_path):
    """读取转录文件并提取文本内容"""
    content = ""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 解析格式：speaker: [timestamp] text
            # 提取文本部分（去掉说话人标识和时间戳）
            if ':' in line and '[' in line and ']' in line:
                # 找到 ] 后的文本
                bracket_end = line.find(']')
                if bracket_end != -1:
                    text = line[bracket_end + 1:].strip()
                    if text:
                        content += text + " "
            else:
                # 如果格式不匹配，直接添加整行
                content += line + " "
        
        return content.strip()
    except Exception as e:
        print(f"读取文件 {file_path} 失败: {e}")
        return ""

def clean_text(text):
    """清理文本，移除说话人标签和无用字符"""
    if pd.isna(text) or text is None:
        return ""
    
    text = str(text).strip()
    
    # 按行分割
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # 移除说话人标签（冒号之前的内容）
        if '：' in line:
            # 找到冒号，取冒号后的内容
            parts = line.split('：', 1)
            if len(parts) > 1:
                content = parts[1].strip()
                if content:
                    cleaned_lines.append(content)
        else:
            # 如果没有冒号，直接添加
            cleaned_lines.append(line)
    
    # 合并所有行
    result = ' '.join(cleaned_lines)
    
    # 移除特殊标记
    result = result.replace('***', '').replace('（空）', '').replace('(空)', '')
    
    return result.strip()

def print_detailed_alignment(result, file_name, asr_text, label_text):
    """打印详细的词级别对照结果"""
    print(f"\n{'='*60}")
    print(f"文件 {file_name} 详细对照")
    print(f"{'='*60}")

    # 显示原始文本
    print(f"ASR文本: {asr_text[:200]}{'...' if len(asr_text) > 200 else ''}")
    print(f"标注文本: {label_text[:200]}{'...' if len(label_text) > 200 else ''}")
    print()

    # 显示对齐结果
    alignment = result.get('alignment', [])
    if not alignment:
        print("无对齐信息")
        return

    # 准备显示格式
    ref_line = "REF: "
    hyp_line = "HYP: "
    op_line = "OP:  "

    max_width = 0
    for item in alignment:
        ref_char = item['ref'] if item['ref'] != '*' else '∅'
        hyp_char = item['hyp'] if item['hyp'] != '*' else '∅'
        max_width = max(max_width, len(ref_char), len(hyp_char), 1)

    # 构建对齐显示（限制显示长度）
    display_limit = 100  # 限制显示的字符数
    for i, item in enumerate(alignment[:display_limit]):
        ref_char = item['ref'] if item['ref'] != '*' else '∅'
        hyp_char = item['hyp'] if item['hyp'] != '*' else '∅'
        op_char = item['op']

        # 确保每个字符占用相同宽度
        width = max(max_width, len(ref_char), len(hyp_char)) + 1

        ref_line += f"{ref_char:<{width}}"
        hyp_line += f"{hyp_char:<{width}}"
        op_line += f"{op_char:<{width}}"

    if len(alignment) > display_limit:
        ref_line += "..."
        hyp_line += "..."
        op_line += "..."

    print(ref_line)
    print(hyp_line)
    print(op_line)
    print()

    # 显示操作说明
    print("操作说明:")
    print("  C = 正确 (Correct)")
    print("  S = 替换 (Substitution)")
    print("  D = 删除 (Deletion)")
    print("  I = 插入 (Insertion)")
    print("  ∅ = 空字符")
    print()

    # 统计信息
    wer = float(result['ins'] + result['sub'] + result['del']) * 100.0 / result['all'] if result['all'] > 0 else 0.0
    print(f"WER统计:")
    print(f"  总字符数: {result['all']}")
    print(f"  正确: {result['cor']} ({result['cor']/result['all']*100:.1f}%)" if result['all'] > 0 else "  正确: 0")
    print(f"  替换: {result['sub']} ({result['sub']/result['all']*100:.1f}%)" if result['all'] > 0 else "  替换: 0")
    print(f"  删除: {result['del']} ({result['del']/result['all']*100:.1f}%)" if result['all'] > 0 else "  删除: 0")
    print(f"  插入: {result['ins']}")
    print(f"  WER: {wer:.2f}%")

def extract_file_key(filename):
    """从文件名提取关键字，用于匹配Excel中的行"""
    # 移除扩展名
    name = Path(filename).stem

    # 提取关键字（如 duihua1 -> duihua1, miantan2 -> miantan2）
    return name.lower()

def create_keyword_mapping():
    """创建文件名关键字到中文的映射"""
    mapping = {
        'beiwanglu1': ['备忘录录音 1', '备忘录 1', '备忘录1', 'beiwang1'],
        'beiwanglu2': ['备忘录录音 2', '备忘录 2', '备忘录2', 'beiwang2'],
        'duihua1': ['对话1', '对话 1', '客户经理一', 'duihua1'],
        'duihua2': ['对话2', '对话 2', '客户经理二', 'duihua2'],
        'duihua3': ['对话3', '对话 3', '客户经理三', 'duihua3'],
        'miantan1': ['面谈1', '面谈 1', '面谈录音 1', 'miantan1'],
        'miantan2': ['面谈2', '面谈 2', '面谈录音 2', 'miantan2'],
        'miantan3': ['面谈3', '面谈 3', '面谈录音 3', 'miantan3'],
        'download': ['download', 'Download']
    }
    return mapping

def find_matching_row(file_key, df):
    """在Excel中查找匹配的行"""
    keyword_mapping = create_keyword_mapping()

    # 获取可能的匹配关键字
    possible_keywords = [file_key]  # 原始关键字
    if file_key in keyword_mapping:
        possible_keywords.extend(keyword_mapping[file_key])

    # 检查每一行的内容，看是否包含任何可能的关键字
    for idx, row in df.iterrows():
        # 检查所有列是否包含任何关键字
        for col in df.columns:
            cell_value = str(row[col]) if pd.notna(row[col]) else ""

            # 检查所有可能的关键字
            for keyword in possible_keywords:
                if keyword.lower() in cell_value.lower():
                    return idx

    return None

def calculate_transcripts_wer():
    """计算transcripts文件夹中的文件与Excel E列的WER"""

    print("=" * 80)
    print("Transcripts文件夹 WER分析")
    print("=" * 80)

    # 读取Excel文件
    excel_file = "new1.xlsx"
    try:
        df = pd.read_excel(excel_file)
        print(f"成功读取Excel文件，共 {len(df)} 行数据")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return None

    # 获取E列（标注结果）
    if len(df.columns) < 5:
        print(f"Excel文件列数不足，只有 {len(df.columns)} 列")
        return None

    label_col = df.iloc[:, 4]  # E列 (标注结果)
    print(f"E列名称: {df.columns[4]}")

    # 获取transcripts文件夹中的所有txt文件
    transcript_files = glob.glob("transcripts/*.txt")
    if not transcript_files:
        print("transcripts文件夹中没有找到txt文件")
        return None

    print(f"找到 {len(transcript_files)} 个转录文件")

    calculator = WERCalculator()
    ignore_words = set()
    case_sensitive = False

    valid_pairs = 0
    individual_results = []

    # 处理每个转录文件
    for transcript_file in sorted(transcript_files):
        file_name = os.path.basename(transcript_file)
        file_key = extract_file_key(file_name)

        print(f"\n--- 处理文件: {file_name} (关键字: {file_key}) ---")

        # 读取转录文件内容
        asr_text = read_transcript_file(transcript_file)
        if not asr_text:
            print(f"跳过空文件: {file_name}")
            continue

        # 在Excel中查找匹配的行
        matching_row_idx = find_matching_row(file_key, df)
        if matching_row_idx is None:
            print(f"在Excel中未找到匹配 '{file_key}' 的行")
            continue

        # 获取对应的标注文本
        label_text = label_col.iloc[matching_row_idx]
        if pd.isna(label_text):
            print(f"第 {matching_row_idx + 1} 行的标注文本为空")
            continue

        print(f"匹配到Excel第 {matching_row_idx + 1} 行")

        # 清理文本
        asr_cleaned = clean_text(asr_text)
        label_cleaned = clean_text(str(label_text))

        if not asr_cleaned or not label_cleaned:
            print(f"清理后文本为空，跳过")
            continue

        valid_pairs += 1

        print(f"ASR文本长度: {len(asr_cleaned)}")
        print(f"标注文本长度: {len(label_cleaned)}")

        # 转换为字符列表
        asr_chars = characterize(asr_cleaned)
        label_chars = characterize(label_cleaned)

        # 标准化处理
        asr_normalized = normalize(asr_chars, ignore_words, case_sensitive)
        label_normalized = normalize(label_chars, ignore_words, case_sensitive)

        print(f"ASR字符数: {len(asr_normalized)}")
        print(f"标注字符数: {len(label_normalized)}")

        # 计算WER
        result = calculator.calculate(label_normalized.copy(), asr_normalized.copy())

        if result['all'] != 0:
            wer = float(result['ins'] + result['sub'] + result['del']) * 100.0 / result['all']
        else:
            wer = 0.0

        individual_results.append({
            'file_name': file_name,
            'file_key': file_key,
            'excel_row': matching_row_idx + 1,
            'wer': wer,
            'result': result,
            'asr_text': asr_text,
            'label_text': str(label_text)
        })

        print(f'WER: {wer:4.2f}% (N={result["all"]} C={result["cor"]} S={result["sub"]} D={result["del"]} I={result["ins"]})')

        # 显示详细的词级别对照
        print_detailed_alignment(result, file_name, asr_text, str(label_text))

    # 计算总体WER
    print("\n" + "=" * 80)
    print("总体结果")
    print("=" * 80)

    overall_result = calculator.overall()
    if overall_result['all'] != 0:
        overall_wer = float(overall_result['ins'] + overall_result['sub'] + overall_result['del']) * 100.0 / overall_result['all']
    else:
        overall_wer = 0.0

    print(f'处理的有效文件数: {valid_pairs}')
    print(f'总体WER: {overall_wer:4.2f}%')
    print(f'总字符数: {overall_result["all"]:,}')
    print(f'正确字符: {overall_result["cor"]:,} ({overall_result["cor"]/overall_result["all"]*100:.1f}%)')
    print(f'替换错误: {overall_result["sub"]:,} ({overall_result["sub"]/overall_result["all"]*100:.1f}%)')
    print(f'删除错误: {overall_result["del"]:,} ({overall_result["del"]/overall_result["all"]*100:.1f}%)')
    print(f'插入错误: {overall_result["ins"]:,} ({overall_result["ins"]/overall_result["all"]*100:.1f}%)')

    # 保存详细结果
    save_detailed_results(individual_results, overall_result, overall_wer, valid_pairs)

    return overall_wer, overall_result, individual_results

def save_detailed_results(individual_results, overall_result, overall_wer, valid_pairs):
    """保存详细的WER分析结果到文件"""
    output_file = "transcripts_wer_detailed_results.txt"

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("Transcripts文件夹 WER详细分析结果\n")
        f.write("=" * 80 + "\n\n")

        # 写入每个文件的详细对照结果
        for result_info in individual_results:
            result = result_info['result']
            file_name = result_info['file_name']
            file_key = result_info['file_key']
            excel_row = result_info['excel_row']
            asr_text = result_info['asr_text']
            label_text = result_info['label_text']

            f.write(f"文件 {file_name} 详细分析\n")
            f.write("=" * 60 + "\n")
            f.write(f"文件关键字: {file_key}\n")
            f.write(f"匹配Excel行: 第{excel_row}行\n")
            f.write(f"ASR文本: {asr_text[:200]}{'...' if len(asr_text) > 200 else ''}\n")
            f.write(f"标注文本: {label_text[:200]}{'...' if len(label_text) > 200 else ''}\n\n")

            # 写入对齐结果
            alignment = result.get('alignment', [])
            if alignment:
                # 准备显示格式
                ref_line = "REF: "
                hyp_line = "HYP: "
                op_line = "OP:  "

                max_width = 0
                for item in alignment:
                    ref_char = item['ref'] if item['ref'] != '*' else '∅'
                    hyp_char = item['hyp'] if item['hyp'] != '*' else '∅'
                    max_width = max(max_width, len(ref_char), len(hyp_char), 1)

                # 构建对齐显示（限制显示长度）
                display_limit = 150  # 文件中可以显示更多
                for i, item in enumerate(alignment[:display_limit]):
                    ref_char = item['ref'] if item['ref'] != '*' else '∅'
                    hyp_char = item['hyp'] if item['hyp'] != '*' else '∅'
                    op_char = item['op']

                    width = max(max_width, len(ref_char), len(hyp_char)) + 1

                    ref_line += f"{ref_char:<{width}}"
                    hyp_line += f"{hyp_char:<{width}}"
                    op_line += f"{op_char:<{width}}"

                if len(alignment) > display_limit:
                    ref_line += "..."
                    hyp_line += "..."
                    op_line += "..."

                f.write(f"{ref_line}\n")
                f.write(f"{hyp_line}\n")
                f.write(f"{op_line}\n\n")

                # 写入操作说明
                f.write("操作说明: C=正确, S=替换, D=删除, I=插入, ∅=空字符\n\n")

                # 写入统计信息
                wer = result_info['wer']
                f.write(f"WER统计:\n")
                f.write(f"  总字符数: {result['all']}\n")
                if result['all'] > 0:
                    f.write(f"  正确: {result['cor']} ({result['cor']/result['all']*100:.1f}%)\n")
                    f.write(f"  替换: {result['sub']} ({result['sub']/result['all']*100:.1f}%)\n")
                    f.write(f"  删除: {result['del']} ({result['del']/result['all']*100:.1f}%)\n")
                else:
                    f.write(f"  正确: 0\n")
                    f.write(f"  替换: 0\n")
                    f.write(f"  删除: 0\n")
                f.write(f"  插入: {result['ins']}\n")
                f.write(f"  WER: {wer:.2f}%\n")

                # 写入错误详情
                errors = []
                for item in alignment:
                    if item['op'] == 'S':
                        errors.append(f"替换: '{item['ref']}' → '{item['hyp']}'")
                    elif item['op'] == 'D':
                        errors.append(f"删除: '{item['ref']}'")
                    elif item['op'] == 'I':
                        errors.append(f"插入: '{item['hyp']}'")

                if errors:
                    f.write(f"\n错误详情 (前50个):\n")
                    for i, error in enumerate(errors[:50], 1):
                        f.write(f"  {i}. {error}\n")
                    if len(errors) > 50:
                        f.write(f"  ... 还有 {len(errors) - 50} 个错误\n")
                else:
                    f.write(f"\n✓ 完全正确，无错误！\n")

            f.write("\n" + "-" * 80 + "\n\n")

        # 写入总体结果
        f.write("总体结果汇总\n")
        f.write("=" * 80 + "\n")
        f.write(f"处理的有效文件数: {valid_pairs}\n")
        f.write(f"总体WER: {overall_wer:4.2f}%\n")
        f.write(f"总字符数: {overall_result['all']:,}\n")
        f.write(f"正确: {overall_result['cor']:,}, 替换: {overall_result['sub']:,}, 删除: {overall_result['del']:,}, 插入: {overall_result['ins']:,}\n")

        # 写入各文件WER汇总
        f.write(f"\n各文件WER汇总:\n")
        for result_info in individual_results:
            result = result_info['result']
            f.write(f"{result_info['file_name']}: WER={result_info['wer']:4.2f}% ")
            f.write(f"(N={result['all']} C={result['cor']} S={result['sub']} D={result['del']} I={result['ins']})\n")

    print(f"\n详细结果已保存到: {output_file}")

    # 创建简化汇总报告
    create_summary_report(individual_results, overall_result, overall_wer, valid_pairs)

def create_summary_report(individual_results, overall_result, overall_wer, valid_pairs):
    """创建简化的汇总报告"""
    output_file = "transcripts_wer_summary.txt"

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("Transcripts文件夹 WER分析汇总报告\n")
        f.write("=" * 50 + "\n\n")

        f.write("文件匹配情况:\n")
        f.write("-" * 30 + "\n")
        for result_info in individual_results:
            f.write(f"{result_info['file_name']:<15} -> Excel第{result_info['excel_row']}行\n")

        f.write(f"\n总体WER结果:\n")
        f.write("-" * 30 + "\n")
        f.write(f"处理文件数: {valid_pairs}\n")
        f.write(f"总体WER: {overall_wer:6.2f}%\n")
        f.write(f"总字符数: {overall_result['all']:,}\n")
        f.write(f"正确率: {overall_result['cor']/overall_result['all']*100:6.1f}%\n")
        f.write(f"替换率: {overall_result['sub']/overall_result['all']*100:6.1f}%\n")
        f.write(f"删除率: {overall_result['del']/overall_result['all']*100:6.1f}%\n")
        f.write(f"插入数: {overall_result['ins']:,}\n")

        f.write(f"\n各文件WER详情:\n")
        f.write("-" * 50 + "\n")
        f.write(f"{'文件名':<15} {'WER%':<8} {'字符数':<8} {'正确率%':<8}\n")
        f.write("-" * 50 + "\n")

        for result_info in sorted(individual_results, key=lambda x: x['wer']):
            result = result_info['result']
            accuracy = result['cor']/result['all']*100 if result['all'] > 0 else 0
            f.write(f"{result_info['file_name']:<15} {result_info['wer']:<8.2f} {result['all']:<8} {accuracy:<8.1f}\n")

        f.write(f"\n性能分析:\n")
        f.write("-" * 30 + "\n")

        # 按WER分类
        excellent = [r for r in individual_results if r['wer'] < 5]
        good = [r for r in individual_results if 5 <= r['wer'] < 10]
        fair = [r for r in individual_results if 10 <= r['wer'] < 20]
        poor = [r for r in individual_results if r['wer'] >= 20]

        f.write(f"优秀 (WER < 5%):  {len(excellent)} 个文件\n")
        f.write(f"良好 (5% ≤ WER < 10%): {len(good)} 个文件\n")
        f.write(f"一般 (10% ≤ WER < 20%): {len(fair)} 个文件\n")
        f.write(f"较差 (WER ≥ 20%): {len(poor)} 个文件\n")

        if excellent:
            f.write(f"\n优秀文件: {', '.join([r['file_name'] for r in excellent])}\n")
        if poor:
            f.write(f"需要改进: {', '.join([r['file_name'] for r in poor])}\n")

    print(f"汇总报告已保存到: {output_file}")

if __name__ == "__main__":
    wer, result, individual_results = calculate_transcripts_wer()
