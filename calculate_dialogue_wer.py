#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import sys
import unicodedata

# 从compute_wer.py复制必要的函数和类
remove_tag = True
spacelist= [' ', '\t', '\r', '\n']
puncts = ['!', ',', '?',
          '、', '。', '！', '，', '；', '？',
          '：', '「', '」', '︰',  '『', '』', '《', '》']

def characterize(string) :
  res = []
  i = 0
  while i < len(string):
    char = string[i]
    if char in puncts:
      i += 1
      continue
    cat1 = unicodedata.category(char)
    if cat1 == 'Zs' or cat1 == 'Cn' or char in spacelist:
       i += 1
       continue
    if cat1 == 'Lo':
       res.append(char)
       i += 1
    else:
       sep = ' '
       if char == '<': sep = '>'
       j = i+1
       while j < len(string):
         c = string[j]
         if ord(c) >= 128 or (c in spacelist) or (c==sep):
           break
         j += 1
       if j < len(string) and string[j] == '>':
         j += 1
       res.append(string[i:j])
       i = j
  return res

def stripoff_tags(x):
  if not x: return ''
  chars = []
  i = 0; T=len(x)
  while i < T:
    if x[i] == '<':
      while i < T and x[i] != '>':
         i += 1
      i += 1
    else:
      chars.append(x[i])
      i += 1
  return ''.join(chars)

def normalize(sentence, ignore_words, cs, split=None):
    new_sentence = []
    for token in sentence:
        x = token
        if not cs:
           x = x.upper()
        if x in ignore_words:
           continue
        if remove_tag:
          x = stripoff_tags(x)
        if not x:
          continue
        if split and x in split:
          new_sentence += split[x]
        else:
          new_sentence.append(x)
    return new_sentence

class Calculator :
  def __init__(self) :
    self.data = {}
    self.space = []
    self.cost = {}
    self.cost['cor'] = 0
    self.cost['sub'] = 1
    self.cost['del'] = 1
    self.cost['ins'] = 1
  def calculate(self, lab, rec) :
    lab.insert(0, '')
    rec.insert(0, '')
    while len(self.space) < len(lab) :
      self.space.append([])
    for row in self.space :
      for element in row :
        element['dist'] = 0
        element['error'] = 'non'
      while len(row) < len(rec) :
        row.append({'dist' : 0, 'error' : 'non'})
    for i in range(len(lab)) :
      self.space[i][0]['dist'] = i
      self.space[i][0]['error'] = 'del'
    for j in range(len(rec)) :
      self.space[0][j]['dist'] = j
      self.space[0][j]['error'] = 'ins'
    self.space[0][0]['error'] = 'non'
    for token in lab :
      if token not in self.data and len(token) > 0 :
        self.data[token] = {'all' : 0, 'cor' : 0, 'sub' : 0, 'ins' : 0, 'del' : 0}
    for token in rec :
      if token not in self.data and len(token) > 0 :
        self.data[token] = {'all' : 0, 'cor' : 0, 'sub' : 0, 'ins' : 0, 'del' : 0}
    for i, lab_token in enumerate(lab) :
      for j, rec_token in enumerate(rec) :
        if i == 0 or j == 0 :
          continue
        min_dist = sys.maxsize
        min_error = 'none'
        dist = self.space[i-1][j]['dist'] + self.cost['del']
        error = 'del'
        if dist < min_dist :
          min_dist = dist
          min_error = error
        dist = self.space[i][j-1]['dist'] + self.cost['ins']
        error = 'ins'
        if dist < min_dist :
          min_dist = dist
          min_error = error
        if lab_token == rec_token :
          dist = self.space[i-1][j-1]['dist'] + self.cost['cor']
          error = 'cor'
        else :
          dist = self.space[i-1][j-1]['dist'] + self.cost['sub']
          error = 'sub'
        if dist < min_dist :
          min_dist = dist
          min_error = error
        self.space[i][j]['dist'] = min_dist
        self.space[i][j]['error'] = min_error
    result = {'lab':[], 'rec':[], 'all':0, 'cor':0, 'sub':0, 'ins':0, 'del':0}
    i = len(lab) - 1
    j = len(rec) - 1
    while True :
      if self.space[i][j]['error'] == 'cor' :
        if len(lab[i]) > 0 :
          self.data[lab[i]]['all'] = self.data[lab[i]]['all'] + 1
          self.data[lab[i]]['cor'] = self.data[lab[i]]['cor'] + 1
          result['all'] = result['all'] + 1
          result['cor'] = result['cor'] + 1
        result['lab'].insert(0, lab[i])
        result['rec'].insert(0, rec[j])
        i = i - 1
        j = j - 1
      elif self.space[i][j]['error'] == 'sub' :
        if len(lab[i]) > 0 :
          self.data[lab[i]]['all'] = self.data[lab[i]]['all'] + 1
          self.data[lab[i]]['sub'] = self.data[lab[i]]['sub'] + 1
          result['all'] = result['all'] + 1
          result['sub'] = result['sub'] + 1
        result['lab'].insert(0, lab[i])
        result['rec'].insert(0, rec[j])
        i = i - 1
        j = j - 1
      elif self.space[i][j]['error'] == 'del' :
        if len(lab[i]) > 0 :
          self.data[lab[i]]['all'] = self.data[lab[i]]['all'] + 1
          self.data[lab[i]]['del'] = self.data[lab[i]]['del'] + 1
          result['all'] = result['all'] + 1
          result['del'] = result['del'] + 1
        result['lab'].insert(0, lab[i])
        result['rec'].insert(0, "")
        i = i - 1
      elif self.space[i][j]['error'] == 'ins' :
        if len(rec[j]) > 0 :
          self.data[rec[j]]['ins'] = self.data[rec[j]]['ins'] + 1
          result['ins'] = result['ins'] + 1
        result['lab'].insert(0, "")
        result['rec'].insert(0, rec[j])
        j = j - 1
      elif self.space[i][j]['error'] == 'non' :
        break
      else :
        print('this should not happen , i = {i} , j = {j} , error = {error}'.format(i = i, j = j, error = self.space[i][j]['error']))
    return result
  def overall(self) :
    result = {'all':0, 'cor':0, 'sub':0, 'ins':0, 'del':0}
    for token in self.data :
      result['all'] = result['all'] + self.data[token]['all']
      result['cor'] = result['cor'] + self.data[token]['cor']
      result['sub'] = result['sub'] + self.data[token]['sub']
      result['ins'] = result['ins'] + self.data[token]['ins']
      result['del'] = result['del'] + self.data[token]['del']
    return result

def read_dialogue_file(filename):
    """读取对话文件并返回对话列表"""
    dialogues = []
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read().strip()
        # 按对话分割
        dialogue_blocks = content.split('\n\n')
        for block in dialogue_blocks:
            if block.strip():
                # 提取对话内容（去掉"对话X："前缀）
                if '：' in block:
                    dialogue_text = block.split('：', 1)[1].strip()
                    dialogues.append(dialogue_text)
    return dialogues

def text_to_words(text):
    """将文本转换为词列表，使用字符级别分割"""
    # 使用characterize函数进行字符级别的分割
    return characterize(text)

def calculate_wer_for_dialogues(ref_file, hyp_file):
    """计算两个对话文件之间的WER"""
    
    # 读取对话文件
    ref_dialogues = read_dialogue_file(ref_file)
    hyp_dialogues = read_dialogue_file(hyp_file)
    
    print(f"参考文件 ({ref_file}) 包含 {len(ref_dialogues)} 个对话")
    print(f"识别文件 ({hyp_file}) 包含 {len(hyp_dialogues)} 个对话")
    print()
    
    # 确保两个文件的对话数量一致
    min_dialogues = min(len(ref_dialogues), len(hyp_dialogues))
    
    calculator = Calculator()
    ignore_words = set()
    case_sensitive = False
    
    # 逐个对话计算WER
    for i in range(min_dialogues):
        print(f"=== 对话 {i+1} ===")
        
        # 转换为词列表
        ref_words = text_to_words(ref_dialogues[i])
        hyp_words = text_to_words(hyp_dialogues[i])
        
        # 标准化处理
        ref_normalized = normalize(ref_words, ignore_words, case_sensitive)
        hyp_normalized = normalize(hyp_words, ignore_words, case_sensitive)
        
        print(f"参考文本长度: {len(ref_normalized)} 字符")
        print(f"识别文本长度: {len(hyp_normalized)} 字符")
        
        # 计算WER
        result = calculator.calculate(ref_normalized.copy(), hyp_normalized.copy())
        
        if result['all'] != 0:
            wer = float(result['ins'] + result['sub'] + result['del']) * 100.0 / result['all']
        else:
            wer = 0.0
            
        print(f'WER: {wer:4.2f}% (N={result["all"]} C={result["cor"]} S={result["sub"]} D={result["del"]} I={result["ins"]})')
        print()
    
    # 计算总体WER
    print("=" * 60)
    print("总体结果:")
    
    overall_result = calculator.overall()
    if overall_result['all'] != 0:
        overall_wer = float(overall_result['ins'] + overall_result['sub'] + overall_result['del']) * 100.0 / overall_result['all']
    else:
        overall_wer = 0.0
        
    print(f'总体WER: {overall_wer:4.2f}%')
    print(f'总字符数: {overall_result["all"]}')
    print(f'正确: {overall_result["cor"]}')
    print(f'替换: {overall_result["sub"]}')
    print(f'删除: {overall_result["del"]}')
    print(f'插入: {overall_result["ins"]}')
    
    return overall_wer, overall_result

if __name__ == "__main__":
    # 计算WER，dialogue_texts2.txt作为参考（答案），dialogue_texts.txt作为识别结果
    ref_file = "dialogue_texts2.txt"  # 参考答案
    hyp_file = "dialogue_texts.txt"   # 识别结果
    
    print("计算对话文本的WER (Word Error Rate)")
    print(f"参考文件（答案）: {ref_file}")
    print(f"识别文件（待评估）: {hyp_file}")
    print()
    
    wer, result = calculate_wer_for_dialogues(ref_file, hyp_file)