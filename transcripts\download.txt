2: [0.440 2.580] 是那个那个那个那个那个东西，
3: [3.380 4.885] 那你们等学试的时候
2: [5.520 14.140] 卡不住，你点结束不也不一样啊。你看你点结束，他也他也许在录制过程中，他点面了，他做输
3: [14.180 19.720] 入的那种点点那种。他等他结束之后，我这时候就可以立刻去识别，我不需要知道
2: [19.960 27.915] 我。这是我是说他在中途的时候，他说他他在这里赢嘛，然后就说他就在里面说有一些东西
3: [29.750 37.330] 总结，这时候那就肯定不需要去识别嘛，因为因为因为你那个肯定会拦截嘛，我不需要去识别啊。
2: [38.930 47.890] 不是，那你我问题是我不告诉你，他是面谈总监，面谈那个啥，你怎么知道就是你你你完全依赖于那个那个录音呢，完全录依赖
3: [47.890 49.710] 那个录音。对呀，我有记录的呀。
2: [50.130 51.470] 你不能这样，怎么
3: [51.470 51.825] 不能？
2: [53.360 60.275] 你现在是只有一个面团机，有面团功能，你后面有其他的那个还有照相照，这是其他的功能的吗？那你这是玩不转的嘞，
3: [62.840 63.805] 你说什么
2: [64.470 70.250] 我不知道，他后面要拓展了怎么办？那就不只是面谈，建议面谈总结这两种，然后可以根据录音的状态去，你
3: [70.570 71.530] 这这也好改啊。
2: [71.910 84.685] 如果在后面，那你这是干脆就是那个地入口里面加一个那个他用户询问的问题的类型呢。如果是面谈这个值，如果是面谈建议的话，你那就就给做保了。那这样说以后可以兼容了，以后的嘛，以后也不用改了，
3: [87.940 88.325] 你会
2: [89.660 97.270] 就那个接口，我会告诉你这个用户当前他的意图是啥，他是要面谈建议和面谈总的就是一个扣的嘛。你拿到这个扣de之
3: [97.270 102.590] 后，其实就传到一个字段。但是就说业务我这边随便是怎么样子是吗？
2: [102.710 106.390] 对你拿那个code,你要是再去把录音给豆包去去去去，
3: [106.850 112.910] 那你传给我也行了，你要传到那边去也行。所时说，我这边我我不会去直接去去识别。
2: [112.910 120.765] 对呀，你只有面谈，使用那个code对应的是面团建议的人能去识别嘛，你就不用依赖那个那个录音的状态去确定。
3: [121.690 132.325] 可以啊，反正现在我可以传给那个参数。我反正如果是就是那个总结的话，我肯定也不会实施嘛，效果是一样嘛，就是说你这边传不传一个参数的问题，
2: [132.950 134.975] 嗯，对吧？补充他就个没啥，
3: [136.270 138.785] 就我这边就无非就多放那么一顿，
2: [139.860 145.925] 你多碰到一个对你，你应该是把之前判断录音的点赚饭钱，判到那个扣子，
3: [147.470 152.320] 就就看到你加的那个字段嘛。我看到你是是建议还是
2: [152.420 152.860] 总结
3: [152.860 157.380] 嘛？嗯，如果我我判断还是是建议的话，我
2: [157.380 158.280] 才去识别
3: [158.380 161.880] 嘛。如果是总结的话，那我就什么都不做嘛，不
2: [162.380 180.900] 是你只用判断，你不用判断他是不是总结，你就判断他是不是是他。如果是是他如不是面谈建议，并且当前在录制中这种的话，你就是还在就是丢给豆包去识别嘛。至于他他现在他是不是他是不是总结，或者他是不是其他的一些什么东西呢？你其实不用判断了，你只有只有这一种。
3: [180.900 185.320] 对呀，你就是判断他是不是那个实施建议嘛，嗯实际建议来就嘛，你其
2: [185.320 185.900] 他就行了，
3: [185.900 187.155] 就就这个意思。思
2: [188.080 188.530] 行啊，
3: [189.190 190.790] 是这个意思，还像没表到
2: [190.790 195.440] 其他意思，对我说你不用再额外判断他是不是面谈成度了。
3: [195.660 200.750] 无非就现那就是是放在你那边来判断，还是放在我这边来判断呢？
2: [201.190 206.820] 我这边判断没用，我这边判断也也不能把东西丢给到。哇，我说我这边判断
3: [207.320 216.300] 可以是现在现在就是你你如果你这边判断的话，就是你就会调我接口或者不调嘛。对，现在就是你这边不
2: [216.300 219.180] 判断我肯定会掉的对，因为我要就是
3: [219.200 231.600] 你直接就是不管什么，那你都就是调我接口嘛。对，是说我这边我这个接口到底要要不要去识别，还是还是愿意干吧？就是我的事嘛，我这边我也就放在你这边，我这
2: [231.600 236.420] 边放在我这边，不是你放我这边判断不行，因为我这边不识识这里去拿路地状了。
2: [238.440 253.590] 我也如把这个网络做账呢，我我们的除非你在入场里面，除非你在divide入场里面肯定是有录制状态呀。如果他是面谈总结，并且你入住状态是入制中，我就给你拦了呀。嗯，是吧，
3: [253.710 255.475] 应该这是不是查询接口
2: [256.200 257.620] 是查询接口啊，
3: [257.620 263.080] 但是你这个传承的不是那个实时识别的就识别的接口吗？不是，这不是我新加的接
2: [263.080 267.280] 口吗？不是哦，就是新加的接口，新加的接口就是查询询它的录制态的。现在
3: [267.380 272.100] 就就是在我新新的接口加一个字段嘛。啊，对啊对啊，那你
2: [272.100 272.945] 查状态是
3: [273.820 275.035] 是那个查询点，
2: [276.160 278.725] 你现在的业务不就是给我查那个入职状态了吗？
3: [279.780 284.820] 怎么看这这一个是原来那个查询接口呀，原来我不是提
2: [284.820 293.100] 供了一个那个你不用啊，你就你就用那个新接口啊，新接口里面新接新接口里面我给你返，我给你
3: [293.760 303.410] 这个直接开那个固定实时返回那个那个一肯定是一直在那个路径中那个状态。呃，不，因为
2: [303.550 311.140] 你那个新接口我给你一个扣的是吧？你返回给我，他是录制中还是怎么的？就是这个识
3: [311.140 325.740] 因你你触发那个识别的，他去异步识别，然后那个录制状态嗯肯定是一直是录制中，它不会有其他状态。然后呢，第二个查询结果才是你异步发起的查询结果。
2: [325.740 331.520] 哎，我没听懂你什么意思啊，你这个错包你还要等他，一直在等他返回给你可以的吗？不
3: [331.520 336.695] 需要我，我就我异步啊，回异吗？录制状态的话，你就触发第二个接口查状态啊。
2: [338.360 339.775] 你你说录制状态啊。
3: [340.500 341.420] 对呀，你不管什么
2: [341.420 350.550] 控制状态，为什么要查电视啊，好像为什么你为什么不能在那个接口里面返回给我呢？你的一个新接口里面，你本来就要写新逻辑啊，我那个
3: [350.550 355.870] 只是只是去识别了一个接口，嗯，没必要在这个不是
2: [356.150 372.350] 你那个接口，你那个新接口就是做两件，事事一一。一个是根据我那个客户的判断，要不要把这个东西丢给豆包去识别。然后的话你这个做完了之后，你就开始就是查询他的录制状态，返回给我。你这个新结果就做这两个简单的事，是不是
3: [372.850 388.060] 有一个点奇怪放在一起，就是统一就是他这个录音的状态是还还其他的字段都放在一起，包括这些信息放在一起，这样不好吧。你当时不是我有两s狗，两个接口都有，就是这种状态
2: [388.060 401.510] 的那你这个里面那那你这个接口，那你这个接口，你的意思是你触发你去把东西丢给豆包了，那个是一个单独的接口，那个接口就只做这个事情。对呀，那我就要调两个接口，
3: [401.770 404.650] 你本身就要调两个接口给你讲这个接口。
2: [404.690 429.810] 我这个不是我调两个接口，是第二两个口，但是我分不统一了。你在待会这边的话，你根本不用不用关心，你有没有识别，我只用关心你是不是在录制。所以我的我当时的想法，就是你在待会这边的话，就是一个简单的接口，就是告诉我录制状态，然后到了要调要调那个agent去做那个实施建议或总结的时候，他到了python这边的时候，我才去自选哪里的那个录音的识别的结果。你你你在待会这边拿到这个额度信息根本没用，用不
3: [429.810 434.390] 到你意思就是说就是前面一步，他可能只会
2: [435.110 436.890] 只需要我只需要触发
3: [436.890 437.510] 一个结果是
2: [437.510 445.710] 吗？对，那个结果我只是拿一些简单的信息嘛，因为我只是要在待会那边去判断，要不要给他拦截，要是要走在现场呢，还是直接给他拦截嘛。之
3: [445.710 448.110] 前不是在你上也是调了一个
2: [448.110 462.565] 查询结果。是啊，那是因为你你只提供了一个，我就直接用了。那其实那里面很多信息我都没用到，我只用到了是否有面谈面谈内容。还有就是录音识别，录音是文件识别状态嘛，我那个录音识别状态就是去判断要不要拦截到那个面谈总结的
3: [464.770 471.730] 对。那那现在你意思到就是说这些其实现在你有用到就是说这些你或者用那
2: [471.730 472.290] 在在个
3: [472.330 484.410] 个不要在那个接口返回，然后就是在识别的时候，然后我直接返回给你是这个意思吗？为啥你这些字段不是也要用到吗？你这些字段就就我在地方调的时候，
2: [484.410 491.745] 这这个这个是因为之前以为这个也没有用到之前不用用的。之前以为是他嘛，之前以为是他呀。
3: [494.370 498.270] 哦你之之前实际实际上你意思就说你想要这个字段是
2: [498.270 520.020] 吗？对我只要只要这两个，一个是有面谈，有没有面谈记录嘛？因如如果他没有面谈记录，或者他在录音中的话，那么这种他如果说的是面谈总结，我就会在代一办那边去给他拿了，就不会中结代码那边的那样。如果他是有面谈记录，并且他不在录音中这种的。他说面谈统没有，那我就返
3: [520.020 526.220] 回吧。你要这个状态，我就在第一个接口，我就返回这个状态给你，就实时返回给你。
2: [526.540 533.275] 哎，他录音中，他这个录音他有十八米的，它识别失来了，
3: [534.420 541.125] 失败失在我这边会重新。但是这个时时时间长度，可能那这边
2: [541.830 564.305] 就是假如说这个人他已经录音完成了，对吧？我他有面谈记录，他有他有，他录音完成了。然后我现在就是来一个面谈，总结结后，我就打到代码上去了。面到栏走面谈的a键盘了。面谈a键来里面就会去拿你的那个面谈的内容嘛。这时候你面谈内容识别失败了，那那那那会等一下
3: [565.300 579.425] 返回失败返可能会返回失败。给你失败的失败的话，然后那个数据应该是空的，就是具体的那个文字记录啊，应该是空的。空的话，那这时候你就可能得
2: [580.220 597.935] 就按我的处理嘛。之前我按这几种处理了，就是只有是他如果输入面谈总结的时候，我只有这个情况才放开过去了，这两种我都难了。这种真的是如果识别失败的话，我是让他提示示，就是说让他重冲试，然后这个的话是让他等待录音完成。
3: [599.970 601.590] 你看对，那没错，
2: [601.990 603.895] 这这个是没错嘛。但这个的话
3: [604.530 605.330] 失败的话，你就
2: [605.330 607.245] 是识识别失败的，就是
3: [607.850 615.570] 删除这个这个很很少了呀，几底概率比较小。我们不考虑这你结算就当那种没归空的数据，那处理一个方程去，
2: [616.410 621.980] 其实调两个结果也还行吧，反正反正时间应该也没什么事，也不会
3: [622.180 626.460] 不会差费用单。对，那就统一，再你就调这个结果了。
2: [626.460 638.570] 你设计吧，你我听你的，你听你的，你你设计吧，你说怎么搞怎么搞，我你到时候把结果提供给我，我跟去啊，你你决定呗，你决定是放一个间，你
3: [638.570 641.090] 决定我就就放这里吧，这肯定比较好维护
2: [641.090 643.035] 嘛，都可以啊。你就你决定
1: [647.290 648.230] 我刚才在录你们的
2: [648.230 650.105] 音，我去你录我们音干嘛？
1: [650.750 664.470] 老婆，他有个问题去了，他实时的那个最开始你们说话，他不展示出来，就是转转文字嘛，转不出来。他录音里面有录音里面，因为走的是原文件识别啊，是不是有
3: [664.470 667.670] 一个问题？因为我靠他是是两个接口
1: [668.370 674.525] 嘛，我知道啊，就是为这个为啥它底层识别的一些那个务应该是同一个呀？
3: [677.870 679.195] 这就可能
1: [681.510 684.905] 我靠，这都给你提，都给你提示太慢了，我操你们两个人说话的
1: [686.980 692.775] 面谈建议吗？嗯，你们的录音都识别成代代码了，派单代码检查、面谈记录和
2: [698.830 699.610] 为什么这样？
1: [699.770 704.190] 你们两刚刚都一直在聊吗？聊这个面谈建议或者那个录音这个东西来吗？啊，这
2: [704.190 704.770] 个接口什
1: [704.770 708.115] 么什么那个对啊，注意试一下。哎，你看一下嘛，
3: [709.360 710.165] 他都挺建议
2: [711.670 714.985] 哦，确实是确实是，哎，确实是在根据
1: [717.140 718.060] 后面总总
3: [718.060 718.680] 结得挺早是
2: [718.680 726.615] 吧。嗯，我是让他面谈，建议是给我进一步谈进一步谈话的建议
2: [729.500 730.995] 是这关键问题
2: [735.920 736.160] 嗯，
1: [737.410 743.105] 他那个实时转的是不是要声音大一点呢？要要很清晰的才能识别出来。
